import { EAddonItemStatus, IAddonItem, ISelectedAddonItem, OutLinedCard, StraightChip } from "@common";
import { SelectAddonItem } from "@common/Addons/AddonGroup/SelectedAddon/SelectedAddonItem";
import { HelpTooltip } from "@common/HelpTooltip";
import { CountTag } from "@components/dataDisplay/CountTag/CountTag";
import { EquipmentDeliveryModal } from "@features/crm/Addons/EquipmentDeliveryModal/EquipmentDeliveryModal";
import { TContactDetails } from "@features/crm/CustomerDetails/ICustomerDetails";
import { AddCircleOutline, ErrorOutline, RemoveCircleOutline } from "@mui/icons-material";
import { Box, Grid, IconButton, Stack, Typography } from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useAddonItemStyle } from "./style";

interface IParams {
    addon: IAddonItem;
    contactDetails?: TContactDetails;
    selectAddon(addonId: string): void;
    unselectAddon(addonId: string): void;
    isAddDisabled: boolean;
    selectedAddOnItemList?: ISelectedAddonItem[];
    isPostpay?: boolean;
}

export const AddonItem = (props: IParams) => {
    const [isProcessing, setIsProcessing] = useState(false);
    const { addon, contactDetails, selectedAddOnItemList, unselectAddon, selectAddon, isPostpay, isAddDisabled } =
        props;
    const { isRecurring, price, description, isCompatible } = addon;
    const { t } = useTranslation(["common", "addon"]);
    const [isEquipmentDeliveryModalOpen, setIsEquipmentDeliveryModalOpen] = useState(false);

    const selectedAddonListLength = selectedAddOnItemList?.length ?? 0;
    const isAddonMaxReached = selectedAddonListLength >= addon.max;
    const isAddonInTermination = selectedAddOnItemList?.some(
        (item) => item.status === EAddonItemStatus.ONGOING_TERMINATION
    );

    const { classes, cx } = useAddonItemStyle({ isAddonMaxReached, isAddonCompatible: isCompatible });

    // Can delete addon if there are acquisition addons (because no status) or all prepay addons are active
    const canDeleteAddon = !selectedAddOnItemList?.some(
        (addonGroupSelectAddon) =>
            addonGroupSelectAddon.status && addonGroupSelectAddon.status !== EAddonItemStatus.ACTIVE
    );

    const addAddon = () => {
        if (isProcessing) return;
        console.log(" ### 1. Presione el boton ###", isProcessing);
        console.log(" ### 2. Deshabilito el boton ###", isProcessing);
        setIsProcessing(true);        
        console.log(" ### 3. Ejecuto la operacion ###", isProcessing);
        if (isPostpay && addon.includedEquipmentsCodes && addon.includedEquipmentsCodes.length > 0) {
            setIsEquipmentDeliveryModalOpen(true);
        } else {
            selectAddon(addon.id);
        }
        console.log(" ### 4. Habilito el boton ###", isProcessing);
        //setIsProcessing(false);
    };

    return (
        <OutLinedCard className={classes.addon}>
            <Grid container spacing={1}>
                <Grid item xs={12}>
                    <Box display="flex" position="absolute" top={-11}>
                        <Stack direction="row" spacing={2}>
                            <StraightChip
                                label={isRecurring ? t("common:recurring") : t("common:onceOff")}
                                variant={isCompatible ? "secondary" : "grey"}
                            />
                            {!isCompatible && (
                                <StraightChip
                                    icon={<ErrorOutline />}
                                    label={t("common:notAvailableForChannel")}
                                    variant="warning"
                                />
                            )}
                        </Stack>
                    </Box>
                    <Grid alignItems="center" container justifyContent="space-between">
                        <Grid className={classes.addonSpecs} item>
                            <Typography className={classes.addonPrice}>
                                {t(isRecurring ? "common:priceAMonth" : "common:price", { price })}
                            </Typography>
                            <Typography className={classes.addonDescription} variant="body2">
                                {description}
                            </Typography>
                        </Grid>
                        <Grid className={classes.addonLineEnd} item>
                            {isAddonInTermination && <HelpTooltip title={t("addon:addonInTermination")} withMargin />}
                            <CountTag
                                count={selectedAddonListLength}
                                isDisabled={!isCompatible || isAddonInTermination}
                                max={addon.max}
                            />
                            {!isPostpay && canDeleteAddon && (
                                <IconButton
                                    aria-label={t("common:remove")}
                                    className={isCompatible ? undefined : classes.disabledColor}
                                    data-testid={`remove-${addon.id}`}
                                    disabled={selectedAddOnItemList?.length === 0}
                                    size="small"
                                    onClick={() => unselectAddon(addon.id)}
                                >
                                    <RemoveCircleOutline
                                        className={cx(
                                            classes.buttonIcon,
                                            selectedAddOnItemList?.length === 0 ? classes.disabledColor : undefined
                                        )}
                                        color="primary"
                                    />
                                </IconButton>
                            )}
                            {isCompatible && (
                                <IconButton
                                    aria-label={t("common:add")}
                                    data-testid={`add-${addon.id}`}
                                    disabled={isAddonMaxReached || isAddDisabled || isAddonInTermination || isProcessing}
                                    size="small"
                                    onClick={() => addAddon()}
                                >
                                    <AddCircleOutline
                                        className={cx(
                                            classes.buttonIcon,
                                            isAddonMaxReached || isAddDisabled || isAddonInTermination
                                                ? classes.disabledColor
                                                : undefined
                                        )}
                                        color="primary"
                                    />
                                </IconButton>
                            )}
                        </Grid>
                    </Grid>
                </Grid>
                {isPostpay &&
                    selectedAddOnItemList?.map((selectedAddon) => (
                        <SelectAddonItem
                            isDisabled={!isCompatible}
                            key={selectedAddon.catalogCode}
                            selectedAddon={selectedAddon}
                            unselectAddon={unselectAddon}
                        />
                    ))}
            </Grid>
            {isPostpay && (
                <EquipmentDeliveryModal
                    addSelectedAddon={selectAddon}
                    addonId={addon.id}
                    contactDetails={contactDetails}
                    handleClose={() => setIsEquipmentDeliveryModalOpen(false)}
                    open={isEquipmentDeliveryModalOpen}
                />
            )}
        </OutLinedCard>
    );
};
