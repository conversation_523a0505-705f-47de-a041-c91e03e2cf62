import { appointmentAPIRoute } from "@api-routes/appointments";
import { IAppointmentSchedule } from "@features/crm/SubscriptionsAddOns/Components/SchedulesTable";
import { fetcher } from "@utils/fetcher";

export interface IAppointmentResponse {
    status: number;
    data?: IAppointmentSchedule[];
    error?: string;
}

export interface IAppointmentRequest {
    addressId: number;
    at: string;
    contactUuid: string;
    correlationId?: string;
    duration?: number;
    reasonCode: string;
    source?: string;
    timeSlotCode: string;
    timeSlot?: {
        code: string;
    };
}

export const GetAppointments = async (contactUUIDS: string): Promise<IAppointmentResponse> => {
    try {
        const response = await fetcher<IAppointmentSchedule[]>(
            `${appointmentAPIRoute()}contact?contactUuids=${contactUUIDS}`,
            {
                method: "GET",
            }
        );

        return {
            status: 200,
            data: response,
        };
    } catch (error: unknown) {
        return {
            status: 500,
            error: "Internal Server Error",
        };
    }
};

export const CreateAppointment = async (body: IAppointmentRequest): Promise<IAppointmentResponse> => {
    const response = await fetcher<IAppointmentSchedule[]>(`${appointmentAPIRoute()}appointments`, {
        method: "POST",
        body: body,
    });

    return { status: 200, data: response };
};

export const UpdateAppointment = async (
    appointmentId: string,
    body: Partial<IAppointmentRequest>
): Promise<IAppointmentResponse> => {
    const response = await fetcher<IAppointmentSchedule[]>(`${appointmentAPIRoute()}appointments/${appointmentId}`, {
        method: "PATCH",
        body: body,
    });

    return { status: 200, data: response };
};

