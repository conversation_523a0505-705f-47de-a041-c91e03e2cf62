import { ScheduleSlotsForm } from "@features/acquisition/PostpayAcquisition/Postpay/common/ScheduleInstallationStep/ScheduleSlotsForm/ScheduleSlotsForm";
import { SlotsForm } from "@features/acquisition/PostpayAcquisition/Postpay/common/ScheduleInstallationStep/SlotsForm/SlotsForm";
import { ILeadAddress } from "@modules/tigoSalesFacade/interfaces/models/ITigoSalesProspectLead";
import { ISlot } from "@modules/tigoSalesFacade/interfaces/responses/ITigoTimeIntervalResponse";
import { Divider, Grid, TextField } from "@mui/material";
import { Breakpoints } from "@utils/breakpoints";
import { useTranslation } from "react-i18next";
import { ConfirmationModal } from "@common";
import { useEffect, useState } from "react";

interface IScheduleProps {
    installationAddress: ILeadAddress | undefined;
    setAvailableSlots: (slots: any) => void;
    availableSlots: ISlot[] | undefined;
    taskType: string;
    callId: string | undefined;
    selectedSlot: ISlot | undefined;
    procesarSlot: (slot: ISlot) => {
        titulo: string;
        jornada: string;
        horario: string;
        time_slot: string;
    };
    handleSlotSelection: (slot: ISlot) => void;
    modalTitle: string;
    isOpen: boolean;
    setIsOpen: (open: boolean) => void;
    handleConfirmAppointment: () => void;
    municipalityStr: string;
    fullWidth?: boolean;
    departmentStr: string;
    districtStr?: string;
    townshipStr?: string;
    neighborhoodStr?: string;
    streetStr?: string;
    houseStr?: string;
    provinceStr?: string;
    isErrorModalOpen?: boolean;
    errorModalMessage?: string;
    handleErrorModalConfirm?: () => void;
}

const ScheduleForm = ({
    installationAddress,
    availableSlots,
    setAvailableSlots,
    taskType,
    callId,
    selectedSlot,
    procesarSlot,
    handleSlotSelection,
    modalTitle,
    isOpen,
    setIsOpen,
    handleConfirmAppointment,
    fullWidth = false,
    districtStr,
    townshipStr,
    neighborhoodStr,
    streetStr,
    houseStr,
    provinceStr,

    isErrorModalOpen = false,
    errorModalMessage = "",
    handleErrorModalConfirm = () => { },
}: IScheduleProps) => {
    const { isXs, isSm } = Breakpoints();
    const { t } = useTranslation(["acquisition", "appointment", "address"]);

    const [tempSlotInfo, setTempSlotInfo] = useState<{
        titulo: string;
        jornada: string;
        horario: string;
        time_slot: string;
    } | null>(null);

    const [confirmedSlotInfo, setConfirmedSlotInfo] = useState<{
        titulo: string;
        jornada: string;
        horario: string;
        time_slot: string;
    } | null>(null);

    useEffect(() => {
        if (isOpen && selectedSlot) {
            setTempSlotInfo(procesarSlot(selectedSlot));
        }
    }, [isOpen, selectedSlot, procesarSlot]);

    const handleClose = () => {
        setTempSlotInfo(null); // Se borra la selección temporal
        setIsOpen(false);
    };

    const handleConfirm = () => {
        if (tempSlotInfo) {
            setConfirmedSlotInfo(tempSlotInfo);
        }
        handleConfirmAppointment();
    };

    return (
        <>
            { }
            <ConfirmationModal handleClose={handleClose} handleConfirm={handleConfirm} open={isOpen} title={modalTitle}>
                <p>{t("appointment:confirmMessage")}</p>
                {tempSlotInfo && (
                    <Grid container marginTop={2} spacing={2}>
                        <Grid item md={6} xs={12}>
                            <TextField
                                disabled
                                fullWidth
                                label={t("appointment:detailAppointment.date")}
                                value={tempSlotInfo.titulo}
                            />
                        </Grid>
                        <Grid item md={6} xs={12}>
                            <TextField disabled fullWidth label={tempSlotInfo.jornada} value={tempSlotInfo.horario} />
                        </Grid>
                    </Grid>
                )}
            </ConfirmationModal>

            {}
            <ConfirmationModal
                open={isErrorModalOpen}
                title={t("appointment:confirmError")}
                handleClose={handleErrorModalConfirm}
                handleConfirm={handleErrorModalConfirm}
            >
                <p>{errorModalMessage}</p>
            </ConfirmationModal>
            <Grid
                alignItems="center"
                container
                justifyContent="center"
                marginTop={2}
                marginX={fullWidth ? "none" : "auto"}
                maxWidth={isXs && isSm ? "100%" : fullWidth ? "100%" : "80%"}
                paddingBottom={2}
                spacing={2}
            >
                <Grid item xs={12}>
                    <Divider textAlign="center">{t("appointment:installationAddress")}</Divider>
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextField disabled fullWidth label={t("appointment:province")} value={provinceStr} />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextField disabled fullWidth label={t("appointment:district")} value={districtStr} />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextField disabled fullWidth label={t("appointment:township")} value={townshipStr} />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextField disabled fullWidth label={t("appointment:neighborhood")} value={neighborhoodStr} />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextField disabled fullWidth label={t("appointment:street")} value={streetStr} />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextField disabled fullWidth label={t("appointment:house")} value={houseStr} />
                </Grid>
                <Grid item xs={12}>
                    <Divider textAlign="center">{t("appointment:checkAvailability")}</Divider>
                </Grid>
                <Grid item xs={12}>
                    <ScheduleSlotsForm
                        address={installationAddress?.descriptionAddress ?? ""}
                        callId={callId ?? ""}
                        district={"PA-SAN FRANCISCO PTY"}
                        //district={installationAddress?.district ?? "PA-SAN FRANCISCO PTY"}
                        latitude={installationAddress?.latitude ?? "27.7439593445784"}
                        longitude={installationAddress?.longitude ?? "130.935331326267"}
                        setAvailableSlots={setAvailableSlots}
                        taskType={taskType}
                        taskTypeCategory=""
                    />
                </Grid>
                {availableSlots && (
                    <>
                        <Grid item xs={12}>
                            <Divider textAlign="center">{t("appointment:availableAppointmentSlots")}</Divider>
                        </Grid>
                        <Grid item xs={12}>
                            { }
                            <SlotsForm availableSlots={availableSlots} setSelectedSlot={handleSlotSelection} />
                        </Grid>
                    </>
                )}
                {confirmedSlotInfo && (
                    <>
                        <Grid item xs={12}>
                            <Divider textAlign="center">{t("appointment:selectedAppointmentDate")}</Divider>
                        </Grid>
                        <Grid item md={6} xs={12}>
                            <TextField
                                disabled
                                fullWidth
                                label={t("appointment:detailAppointment.date")}
                                value={confirmedSlotInfo.titulo}
                            />
                        </Grid>
                        <Grid item md={6} xs={12}>
                            <TextField
                                disabled
                                fullWidth
                                label={confirmedSlotInfo.jornada}
                                value={confirmedSlotInfo.horario}
                            />
                        </Grid>
                    </>
                )}
            </Grid>
        </>
    );
};

export default ScheduleForm;
