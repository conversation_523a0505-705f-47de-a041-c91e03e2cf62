import { EBroadbandTechnologyPriorityName, IConfig } from "./IConfig";

export const config: IConfig = {
    featureFlags: {
        acquisitionPostpay: true,
        identityChecker: true,
        tracing: true,
        revalidateOnFocus: false,
        showPSTNBarrings: true,
        showPSTNUsages: true,
        buyAccessories: true,
        crossSell: true,
        simReplacement: true,
    },
    staticEndpoint: "https://crm-ui-eir-dev.s3.fr-par.scw.cloud",
    timezone: "Europe/Dublin",
    reactivationDays: 60,
    notificationTemplateLocale: "en_IE",
    manualPaymentChargeCatalogCode: "MAN_PAYMENT_HANDLING_C",
    manualDefaultPaymentChargeCatalogCode: "MAN_PAYMENT_RECURRING_C",
    manualPaymentChargeReasonCode: "MANUAL_PAYMENT",
    paperBillChargeCatalogCode: "BILL_CHG",
    authorizedServicesGroupForManageCreditLimit: ["MOBILE"],
    customerNotificationTemplateReferenceList: [
        "RR_1",
        "RR_2",
        "RR_3",
        "RR_4",
        "RR_5",
        "RR_6",
        "RR_8",
        "RR_9",
        "RR_10",
        "RR_11",
        "UND1",
        "UND2",
        "UND3",
        "UND4",
        "UND5",
        "UND6",
        "UND7",
        "UND8",
        "UND9",
        "Undeliverable_PENDING_ORDER",
        "Undeliverable_C_R",
        "Undeliverable_FCS",
        "Undeliverable_Code_72",
        "Undeliverable_Demerge",
        "Undeliverable_Ducting",
        "Undeliverable_Manual SMS",
    ],
    broadbandTechnologyPriority: {
        [EBroadbandTechnologyPriorityName.FTTH]: 10,
        [EBroadbandTechnologyPriorityName.NBI]: 20,
        [EBroadbandTechnologyPriorityName.FTTC]: 30,
        [EBroadbandTechnologyPriorityName.MBB_HIGH]: 40,
        [EBroadbandTechnologyPriorityName.NGB]: 50,
        [EBroadbandTechnologyPriorityName.MBB_LOW]: 60,
    },
    cartVatIncluded: true//String(process.env.CART_VAT_INCLUDED) === "true",
};
