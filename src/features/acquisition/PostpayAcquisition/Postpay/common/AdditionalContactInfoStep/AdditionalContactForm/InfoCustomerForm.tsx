import { IAdditionalContactInfo } from "../IAdditionalContactInfo";
import { UseFormReturn, FormProvider, Controller } from "react-hook-form";
import { IAdditionalCustomerInfo } from "../IPrimaryContactInfo";
import { Checkbox, FormControlLabel, Grid, TextField } from "@mui/material";
import { ContactPermissionSkeleton, FormButton, TextFieldController, useSnackBar } from "@common";
import { useTranslation } from "react-i18next";
import { Breakpoints } from "@utils/breakpoints";
import React, { useEffect, useState } from "react";
import { useGenerateOtp } from "./useGenerateOtp";
interface IParams {
    additionalContactInfo?: IAdditionalContactInfo;
    formMethods: UseFormReturn<IAdditionalCustomerInfo>;
    setIsFormValid: (isValid: boolean) => void;
    setIsOtpValidated: (isValidated: boolean) => void;
}

export const InfoCustomerForm = (props: IParams) => {
    const { formMethods, setIsFormValid, setIsOtpValidated } = props;
    const { t } = useTranslation(["customer", "acquisition", "additionalContactInfo", "validation", "address"]);
    const {
        control,
        formState: { isValid },
        trigger,
        setValue,
        clearErrors,
    } = formMethods;

    const { generateOtp, validateOtpCode, loading } = useGenerateOtp();

    const { isXs, isSm } = Breakpoints();

    const [isContributor, setIsContributor] = useState(false);

    const { setSnackBarSuccess, setSnackBarError } = useSnackBar();

    const [uuidSend, setUuid] = useState<string | null>(null);

    const [isOtpValidated, setIsOtpValidatedState] = useState(true); // by default valid otp btn is disabled


    const handleCheckboxChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const isChecked = event.target.checked;
        setIsContributor(isChecked);

        if (!isChecked) {
            setValue("taxVerificationDigit", "");
            clearErrors("taxVerificationDigit");
        }
        
        await trigger("taxVerificationDigit");
    };

    const handleGenerateOtp = async () => {
        const email = formMethods.getValues("email") ?? "";
        const msisdn = formMethods.getValues("phoneNumber") ?? "";
        setIsOtpValidated(false);
        try {
            const response = await generateOtp(msisdn, email);
            const {
                data: { uuid },
                success,
            } = response;
            if (success) {
                setIsOtpValidatedState(false); // Disable the validate OTP button
                setUuid(uuid);
                setSnackBarSuccess("Codigo de verifiación enviado");
            } else {
                setSnackBarError("Error al enviar el código de verificación");
            }
        } catch (err) {
            setSnackBarError("Error al enviar el código de verificación");
        }
    };

    const handleValidateOtp = async () => {
        const otpCode = formMethods.getValues("otp") ?? "";

        if( !otpCode) {
            setSnackBarError("El código de verificación es obligatorio");
            return;
        }

        if (!uuidSend) {
            setSnackBarError("UUID no encontrado. Genera un OTP primero.");
            return;
        }

        try {
            const response = await validateOtpCode(otpCode, uuidSend);
            const { success } = response;
            console.log(response);
            if (success) {
                setSnackBarSuccess("Código de verificación validado correctamente");
                setIsOtpValidated(true);

                 //set disabled state for validate OTP button
                setIsOtpValidatedState(true); 
            } else {
                setSnackBarError("El código ingresado no es válido o ha expirado, intente de nuevo.");
                setIsOtpValidated(false);
            }
        } catch (err) {
            setSnackBarError("Error al validar el código de verificación");
            setIsOtpValidated(false);
        }

        formMethods.setValue("otp", ""); // Clear the OTP field after validation
    };

    useEffect(() => {
        setIsFormValid(isValid);
    }, [isValid, setIsFormValid]);

    return (
        <FormProvider {...formMethods}>
            <Grid
                alignItems="center"
                container
                justifyContent="center"
                marginX="auto"
                marginY={2}
                maxWidth={isXs && isSm ? "100%" : "80%"}
                spacing={2}
            >
                <Grid item md={6} xs={12}>
                    <TextFieldController
                        control={control}
                        fullWidth
                        label={`${t("customer:name")} *`}
                        name="names"
                        rules={{
                            required: "El nombre es obligatorio",
                            pattern: {
                                value: /^[a-zA-Z ]+$/,
                                message: "Solo caracteres alfabéticos",
                            },
                        }}
                    />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextFieldController
                            control={control}
                            fullWidth
                            label={`${t("customer:lastName")} *`}
                            name="lastName"
                            rules={{
                                required: "El apellido es obligatorio",
                                pattern: {
                                    value: /^[a-zA-Z ]+$/,
                                message: "Solo caracteres alfabéticos",
                                },
                            }}
                        />
                </Grid>
                
                <Grid item md={6} xs={12}>
                    <TextFieldController
                        control={control}
                        fullWidth
                        label={`${t("customer:phoneNumber")} *`}
                        name="phoneNumber"
                        rules={{
                            required: "El número de teléfono es obligatorio",
                            pattern: {
                                value: /^\d{7,8}$/,
                                message: "Solo números y rango de 7 a 8 dígitos",
                            },
                        }}
                    />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextFieldController
                        control={control}
                        fullWidth
                        label={`${t("customer:secondPhoneNumber")} *`}
                        name="secondPhoneNumber"
                        rules={{
                            required: "El segundo número de teléfono es obligatorio",
                            pattern: {
                                value: /^\d{7,8}$/,
                                message: "Solo números y rango de 7 a 8 dígitos",
                            },
                        }}
                    />
                </Grid>
                <Grid alignItems="flex-start" container item md={6} spacing={1} xs={12}>
                    <Grid item xs={8}>
                        <TextFieldController
                            control={control}
                            fullWidth
                            label={`${t("customer:emailCheckLabel")} *`}
                            name="email"
                            rules={{
                                required: "El correo electrónico es obligatorio",
                                pattern: {
                                    value: /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/,
                                    message: "Correo electrónico inválido",
                                },
                            }}
                            
                            onChange={(e) => {
                                setIsOtpValidated(false);
                            }}
                        />
                    </Grid>
                    <Grid item xs={4}>
                        <FormButton
                            minHeight={40}
                            useCustomStyle={true}
                            buttonText={t("customer:generateOtp")}
                            disabled={loading}
                            fullWidth
                            name="generateOtp"
                            variant="contained"
                            onClick={handleGenerateOtp}
                        />
                    </Grid>
                </Grid>
                <Grid alignItems="flex-start"  container item md={6} spacing={1} xs={12}>
                    <Grid item xs={8}>
                        <TextFieldController control={control} fullWidth label={`${t("customer:otp")} *`} name="otp" />
                    </Grid>
                    <Grid item xs={4}>
                        <FormButton
                            minHeight={40}
                            useCustomStyle={true}
                            buttonText={t("customer:validateOtp")}
                            fullWidth
                            disabled={isOtpValidated}
                            name="checkCoverageBtn"
                            variant="contained"
                            onClick={handleValidateOtp}
                        />
                    </Grid>
                </Grid>
                <Grid item md={12} xs={12}>
                    <TextFieldController
                        control={control}
                        fullWidth
                        label={`${t("address:addressDetail")}`}
                        name="address"
                    />
                </Grid>
                <Grid alignItems="center" container item md={12} spacing={1} xs={12}>
                    <Grid item xs={6}>
                        <FormControlLabel
                            control={<Checkbox checked={isContributor} onChange={(e) => {
                                handleCheckboxChange(e);
                                setValue("taxPayer", e.target.checked);
                                trigger("taxVerificationDigit");
                            }} />}
                            label={`${t("customer:taxpayer")}`}
                            name="taxPayer"
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Controller
                            control={control}
                            name="taxVerificationDigit"
                            rules={{
                                    validate: (value) => {
                                        const contributor = formMethods.getValues("taxPayer");
                                        if (contributor && !value) {
                                            return "El dígito verificador es obligatorio";
                                        }
                                        if (value && !/^[0-9]{2}$/.test(value)) {
                                            return "El dígito verificador debe ser mínimo 2 dígitos";
                                        }
                                        return true;
                                    },
                                }}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    disabled={!isContributor}
                                    error={Boolean(formMethods.formState.errors.taxVerificationDigit)}
                                    fullWidth
                                    helperText={formMethods.formState.errors.taxVerificationDigit?.message}
                                    label={`${t("customer:taxVerificationDigit")}`}
                                />
                            )}
                            
                        />
                    </Grid>
                </Grid>
            </Grid>
        </FormProvider>
    ); 
    
    /*return (
        <FormProvider {...formMethods}>
            <Grid
                alignItems="center"
                container
                justifyContent="center"
                marginX="auto"
                marginY={2}
                maxWidth={isXs && isSm ? "100%" : "80%"}
                spacing={2}
            >
                <Grid item md={6} xs={12}>
                    <TextFieldController
                        control={control}
                        fullWidth
                        label={`${t("customer:firstName")} *`}
                        name="names"
                        rules={{
                            required: "El primer nombre es obligatorio",
                            pattern: {
                                value: /^[a-zA-Z0-9]+$/,
                                message: "Solo caracteres alfanuméricos",
                            },
                        }}
                    />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextFieldController
                            control={control}
                            fullWidth
                            label={`${t("customer:lastName")} *`}
                            name="lastName"
                            rules={{
                                required: "El primer apellido es obligatorio",
                                pattern: {
                                    value: /^[a-zA-Z0-9]+$/,
                                    message: "Solo caracteres alfanuméricos",
                                },
                            }}
                        />
                </Grid>
                
                <Grid item md={6} xs={12}>
                    <TextFieldController
                        control={control}
                        fullWidth
                        label={`${t("customer:phoneNumber")} *`}
                        name="phoneNumber"
                        rules={{
                            required: "El número de teléfono es obligatorio",
                            pattern: {
                                value: /^[0-9]{8,}$/,
                                message: "Solo números y mínimo 8 dígitos",
                            },
                        }}
                    />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextFieldController
                        control={control}
                        fullWidth
                        label={`${t("customer:secondPhoneNumber")} *`}
                        name="secondPhoneNumber"
                        rules={{
                            required: "El segundo número de teléfono es obligatorio",
                            pattern: {
                                value: /^[0-9]{8,}$/,
                                message: "Solo números y mínimo 8 dígitos",
                            },
                        }}
                    />
                </Grid>
                <Grid alignItems="flex-start" container item md={6} spacing={1} xs={12}>
                    <Grid item xs={8}>
                        <TextFieldController
                            control={control}
                            fullWidth
                            label={`${t("customer:emailCheckLabel")} *`}
                            name="email"
                            rules={{
                                required: "El correo electrónico es obligatorio",
                                pattern: {
                                    value: /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/,
                                    message: "Correo electrónico inválido",
                                },
                            }}
                        />
                    </Grid>
                    <Grid item xs={4}>
                        <FormButton
                            minHeight={40}
                            useCustomStyle={true}
                            buttonText={t("customer:generateOtp")}
                            disabled={loading}
                            fullWidth
                            name="generateOtp"
                            variant="contained"
                            onClick={handleGenerateOtp}
                        />
                    </Grid>
                </Grid>
                <Grid alignItems="flex-start"  container item md={6} spacing={1} xs={12}>
                    <Grid item xs={8}>
                        <TextFieldController control={control} fullWidth label={`${t("customer:otp")} *`} name="otp" />
                    </Grid>
                    <Grid item xs={4}>
                        <FormButton
                            minHeight={40}
                            useCustomStyle={true}
                            buttonText={t("customer:validateOtp")}
                            fullWidth
                            name="checkCoverageBtn"
                            variant="contained"
                            onClick={handleValidateOtp}
                        />
                    </Grid>
                </Grid>
                <Grid item md={12} xs={12}>
                    <TextFieldController
                        control={control}
                        fullWidth
                        label={`${t("address:addressDetail")}`}
                        name="address"
                    />
                </Grid>
                <Grid alignItems="center" container item md={12} spacing={1} xs={12}>
                    <Grid item xs={6}>
                        <FormControlLabel
                            control={<Checkbox checked={isContributor} onChange={handleCheckboxChange} />}
                            label={`${t("customer:taxpayer")}`}
                            name="taxPayer"
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Controller
                            control={control}
                            name="taxVerificationDigit"
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    disabled={!isContributor}
                                    error={Boolean(formMethods.formState.errors.taxVerificationDigit)}
                                    fullWidth
                                    helperText={formMethods.formState.errors.taxVerificationDigit?.message}
                                    label={`${t("customer:taxVerificationDigit")}`}
                                />
                            )}
                            rules={{
                                required: isContributor ? "El dígito verificador es obligatorio" : false,
                            }}
                        />
                    </Grid>
                </Grid>
            </Grid>
        </FormProvider>
    ); 
    
    /*return (
        <FormProvider {...formMethods}>
            <Grid
                alignItems="center"
                container
                justifyContent="center"
                marginX="auto"
                marginY={2}
                maxWidth={isXs && isSm ? "100%" : "80%"}
                spacing={2}
            >
                <Grid item md={6} xs={12}>
                    <TextFieldController
                        control={control}
                        fullWidth
                        label={`${t("customer:firstName")} *`}
                        name="names"
                        rules={{
                            required: "El primer nombre es obligatorio",
                            pattern: {
                                value: /^[a-zA-Z0-9]+$/,
                                message: "Solo caracteres alfanuméricos",
                            },
                        }}
                    />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextFieldController
                        control={control}
                        fullWidth
                        label={`${t("customer:secondName")} *`}
                        name="secondName"
                        rules={{
                            required: "El segundo nombre es obligatorio",
                            pattern: {
                                value: /^[a-zA-Z0-9]+$/,
                                message: "Solo caracteres alfanuméricos",
                            },
                        }}
                    />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextFieldController
                        control={control}
                        fullWidth
                        label={`${t("customer:lastName")} *`}
                        name="lastName"
                        rules={{
                            required: "El primer apellido es obligatorio",
                            pattern: {
                                value: /^[a-zA-Z0-9]+$/,
                                message: "Solo caracteres alfanuméricos",
                            },
                        }}
                    />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextFieldController
                        control={control}
                        fullWidth
                        label={`${t("customer:secondLastName")} *`}
                        name="secondLastName"
                        rules={{
                            required: "El segundo apellido es obligatorio",
                            pattern: {
                                value: /^[a-zA-Z0-9]+$/,
                                message: "Solo caracteres alfanuméricos",
                            },
                        }}
                    />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextFieldController
                        control={control}
                        fullWidth
                        label={`${t("customer:phoneNumber")} *`}
                        name="phoneNumber"
                        rules={{
                            required: "El número de teléfono es obligatorio",
                            pattern: {
                                value: /^[0-9]{8,}$/,
                                message: "Solo números y mínimo 8 dígitos",
                            },
                        }}
                    />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextFieldController
                        control={control}
                        fullWidth
                        label={`${t("customer:secondPhoneNumber")} *`}
                        name="secondPhoneNumber"
                        rules={{
                            required: "El segundo número de teléfono es obligatorio",
                            pattern: {
                                value: /^[0-9]{8,}$/,
                                message: "Solo números y mínimo 8 dígitos",
                            },
                        }}
                    />
                </Grid>
                <Grid alignItems="center" container item md={6} spacing={1} xs={12}>
                    <Grid item xs={8}>
                        <TextFieldController
                            control={control}
                            fullWidth
                            label={`${t("customer:emailCheckLabel")} *`}
                            name="email"
                            rules={{
                                required: "El correo electrónico es obligatorio",
                                pattern: {
                                    value: /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/,
                                    message: "Correo electrónico inválido",
                                },
                            }}
                        />
                    </Grid>
                    <Grid item xs={4}>
                        <FormButton
                            buttonText={t("customer:generateOtp")}
                            disabled={loading}
                            fullWidth
                            name="generateOtp"
                            variant="contained"
                            onClick={handleGenerateOtp}
                        />
                    </Grid>
                </Grid>
                <Grid alignItems="center" container item md={6} spacing={1} xs={12}>
                    <Grid item xs={8}>
                        <TextFieldController control={control} fullWidth label={`${t("customer:otp")} *`} name="otp" />
                    </Grid>
                    <Grid item xs={4}>
                        <FormButton
                            buttonText={t("customer:validateOtp")}
                            fullWidth
                            name="checkCoverageBtn"
                            variant="contained"
                            onClick={handleValidateOtp}
                        />
                    </Grid>
                </Grid>
                <Grid item md={12} xs={12}>
                    <TextFieldController
                        control={control}
                        fullWidth
                        label={`${t("address:addressDetail")}`}
                        name="address"
                    />
                </Grid>
                <Grid alignItems="center" container item md={12} spacing={1} xs={12}>
                    <Grid item xs={6}>
                        <FormControlLabel
                            control={<Checkbox checked={isContributor} onChange={handleCheckboxChange} />}
                            label={`${t("customer:taxpayer")}`}
                            name="taxPayer"
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Controller
                            control={control}
                            name="taxVerificationDigit"
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    disabled={!isContributor}
                                    error={Boolean(formMethods.formState.errors.taxVerificationDigit)}
                                    fullWidth
                                    helperText={formMethods.formState.errors.taxVerificationDigit?.message}
                                    label={`${t("customer:taxVerificationDigit")}`}
                                />
                            )}
                            rules={{
                                required: isContributor ? "El dígito verificador es obligatorio" : false,
                            }}
                        />
                    </Grid>
                </Grid>
            </Grid>
        </FormProvider>
    ); */
};
