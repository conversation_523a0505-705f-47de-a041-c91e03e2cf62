/* eslint-disable react-hooks/exhaustive-deps */
import { IOption, useSnackBar } from "@common";
import { useFetchState } from "@hooks/useFetchState";
import { useForm } from "@hooks/useForm";
import {
    getDistricts,
    getNeighborhoods,
    getProvinces,
    getStreets,
    getTownships,
    getHouses,
    checkCoverage,
} from "@modules/addressesTigo/apis/apis";
// import { getNormalizedAddress } from "@modules/tigoSalesFacade/apis/v1/addresses";
import { useCallback, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { COLOMBIA_COUNTRY_CODE } from "../constants";
import { INormalizedAddressTigoForm } from "../NormalizedAddressTigoForm/INormalizedAddressTigoForm";
import { INaturalAddressTigoForm } from "./INaturalAddressTigoForm";

interface IParams {
    setnormalizedAddress(value: INormalizedAddressTigoForm | undefined): void;
    setCoverage?: (value: string | undefined) => void;
    initialAddress?: INaturalAddressTigoForm;
    saveNaturalAddress?: (value: INaturalAddressTigoForm) => void;
    installationAvailable?: boolean;
}

export const useNaturalAddressTigoForm = (props: IParams) => {
    const { t } = useTranslation(["validation"]);
    const { initialAddress, setnormalizedAddress, setCoverage, saveNaturalAddress } = props;
    const { startFetching, endFetchingError, endFetching, isLoading } = useFetchState();
    const isResetDisabled = useRef(0);

    const [departments] = useState<IOption[]>([]);
    const [municipalities] = useState<IOption[]>([]);

    const [provinces, setProvinces] = useState<IOption[]>([]);
    const [districts, setDistricts] = useState<IOption[]>([]);
    const [townships, setTownships] = useState<IOption[]>([]);
    const [neighborhoods, setNeighborhoods] = useState<IOption[]>([]);
    const [streets, setStreets] = useState<IOption[]>([]);
    const [houses, setHouses] = useState<IOption[]>([]);

    const { setSnackBarSuccess, setSnackBarError } = useSnackBar();

    const defaultValues: INaturalAddressTigoForm = {
        country: COLOMBIA_COUNTRY_CODE,
        province: "",
        district: "",
        township: "",
        neighborhood: "",
        street: "",
        house: "",
        department: "",
        municipality: "",
        additionalInformation: "",
        roadNumber: "",
        roadType: "",
        provinceCode: "",
        districtCode: "",
        subDistrictCode: "",
        neighborhoodCode: "",
        streetCode: "",
        houseCode: "",
    };

    const formMethods = useForm<INaturalAddressTigoForm>({
        mode: "onChange",
        defaultValues: initialAddress ? { ...initialAddress, department: "", municipality: "" } : defaultValues,
    });

    const { formState, control, getValues, setValue, handleSubmit, trigger, setError } = formMethods;
    const naturalAddress = getValues();

    const loadProvinces = async () => {
        try {
            startFetching();
            const provincesList = await getProvinces();
            endFetching();
            setProvinces(provincesList);
        } catch (e) {
            endFetchingError(e);
        }
    };

    const loadDistricts = async (idProvince: string) => {
        try {
            startFetching();
            const districtsList = await getDistricts(idProvince);
            endFetching();
            setDistricts(districtsList);
        } catch (e) {
            endFetchingError(e);
        }
    };

    const loadTownships = async (idDistrict: string) => {
        try {
            startFetching();
            const townshipsList = await getTownships(idDistrict);
            endFetching();
            setTownships(townshipsList);
        } catch (e) {
            endFetchingError(e);
        }
    };

    const loadNeighborhoods = async (idCorregimiento: string) => {
        try {
            startFetching();
            const neighborhoodsList = await getNeighborhoods(idCorregimiento);
            endFetching();
            setNeighborhoods(neighborhoodsList);
        } catch (e) {
            endFetchingError(e);
        }
    };

    const loadStreets = async (idBarrio: string) => {
        try {
            startFetching();
            const streetsList = await getStreets(idBarrio);
            endFetching();
            setStreets(streetsList);
        } catch (e) {
            endFetchingError(e);
        }
    };

    const loadHouses = async (idCalle: string) => {
        try {
            startFetching();
            const housesList = await getHouses(idCalle);
            endFetching();
            setHouses(housesList);
        } catch (e) {
            endFetchingError(e);
        }
    };

    const handleCheckCoverage = async () => {
        setCoverage!("");
        try {
            startFetching();

            const { neighborhood, street, house } = naturalAddress;

            if (!neighborhood || !street || !house) {
                throw new Error("Faltan datos para verificar cobertura.");
            }
            
            const coverageResult = await checkCoverage(neighborhood, street, house);
            endFetching();

            const hasCoverage = coverageResult.successful;
            if (setCoverage != null) {
                setCoverage(hasCoverage ? "SI" : "NO");
            }

            if (hasCoverage) {
                setSnackBarSuccess(coverageResult.message);
            } else {
                setSnackBarError(coverageResult.message);
            }
        } catch (e) {
            endFetchingError(e);
        }
    };

    useEffect(() => {
        if (naturalAddress.street) {
            loadHouses(naturalAddress.street);
            setValue("house", "");
            trigger("house");
        }
    }, [naturalAddress.street]);

    useEffect(() => {
        if (naturalAddress.neighborhood) {
            loadStreets(naturalAddress.neighborhood);
            setValue("street", "");
            trigger("street");
        }
    }, [naturalAddress.neighborhood]);

    useEffect(() => {
        if (naturalAddress.township) {
            loadNeighborhoods(naturalAddress.township);
            setValue("neighborhood", "");
            trigger("neighborhood");
        }
    }, [naturalAddress.township]);

    useEffect(() => {
        loadProvinces().then(() => {
            if (initialAddress) {
                setValue("department", initialAddress.department);
                trigger("department");
            }
        });
    }, [initialAddress]);

    useEffect(() => {
        if (naturalAddress.province) {
            loadDistricts(naturalAddress.province);
            setValue("district", "");
            trigger("district");
        }
    }, [naturalAddress.province]);

    useEffect(() => {
        if (naturalAddress.district) {
            loadTownships(naturalAddress.district);
            setValue("township", "");
            trigger("township");
        }
    }, [naturalAddress.district]);

    const rules = {
        required: t("validation:required"),
    };

    const resetNormalizedAddress = useCallback(() => {
        if (isResetDisabled.current < 4) {
            isResetDisabled.current++;

            return;
        }

        if (setCoverage) {
            setCoverage("");
        }
        setnormalizedAddress(undefined);
    }, [setCoverage, setnormalizedAddress]);

    // useEffect(() => {
    //     setValue("municipality", "");
    //     resetNormalizedAddress();
    //     if (naturalAddress?.department) {
    //         loadMunicipalities(naturalAddress.department);
    //     }
    // }, [naturalAddress?.department]);

    useEffect(() => {
        resetNormalizedAddress();
    }, [
        naturalAddress.additionalInformation,
        naturalAddress.municipality,
        naturalAddress.roadNumber,
        naturalAddress.roadType,
    ]);

    const handleNormalize = (data: INaturalAddressTigoForm) => {
        if (saveNaturalAddress) {
            saveNaturalAddress(data);
        }

       // resetNormalizedAddress();
        try {
            startFetching();
            // const addressResponse = await getNormalizedAddress(data);
            const provStr = provinces.find((item) => item.value === data.province);
            const distStr = districts.find((item) => item.value === data.district);
            const townStr = townships.find((item) => item.value === data.township);
            const neighStr = neighborhoods.find((item) => item.value === data.neighborhood);
            const streetStr = streets.find((item) => item.value === data.street);
            const houseStr = houses.find((item) => item.value === data.house);

            const provinceCodeStr = provinces.find((item) => item.value === data.province);
            const districtCodeStr = districts.find((item) => item.value === data.district);
            const subDistrictCodeStr = townships.find((item) => item.value === data.township);
            const neighborhoodCodeStr = neighborhoods.find((item) => item.value === data.neighborhood);
            const streetCodeStr = streets.find((item) => item.value === data.street);
            const houseCodeStr = houses.find((item) => item.value === data.house);

            const normalizedAddress: INormalizedAddressTigoForm = {
                province: provStr?.label,
                district: distStr?.label,
                subDistrict: townStr?.label,
                neighborhood: neighStr?.label,
                street: streetStr?.label,
                house: houseStr?.label,
                provinceCode: provinceCodeStr?.value,
                districtCode: districtCodeStr?.value,
                subDistrictCode: subDistrictCodeStr?.value,
                neighborhoodCode: neighborhoodCodeStr?.value,
                streetCode: streetCodeStr?.value,
                houseCode: houseCodeStr?.value
            };

            setnormalizedAddress(normalizedAddress);
            endFetching();
        } catch (e) {
            endFetchingError(e);
        }
    };

    const clearAddressFrom = (level: keyof INaturalAddressTigoForm) => {
        const resetMap: { [K in keyof INaturalAddressTigoForm]?: (keyof INaturalAddressTigoForm)[] } = {
            province: ["district", "township", "neighborhood", "street", "house"],
            district: ["township", "neighborhood", "street", "house"],
            township: ["neighborhood", "street", "house"],
            neighborhood: ["street", "house"],
            street: ["house"],
        };

        const fieldsToClear = resetMap[level] ?? [];

        fieldsToClear.forEach((field) => {
            setValue(field, "");
        });
    };

    return {
        isLoading,
        handleNormalize,
        handleCheckCoverage,
        rules,
        clearAddressFrom,
        provinces: provinces ?? [],
        districts: districts ?? [],
        townships: townships ?? [],
        neighborhoods: neighborhoods ?? [],
        streets: streets ?? [],
        houses: houses ?? [],
        departments: departments ?? [],
        municipalities: municipalities ?? [],

        naturalAddress,

        formState,
        control,
        setValue,
        trigger,
        handleSubmit,
        setError,
    };
};
