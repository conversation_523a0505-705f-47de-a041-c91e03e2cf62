import { IProspect } from "@features/acquisition/IAcquisition";
import { useTranslation } from "react-i18next";
import { KeyedMutator } from "swr";
import { useCallback, useEffect, useState } from "react";
import { useFetchState } from "@hooks/useFetchState";
import {
    ILeadAddress,
    ILeadCustomer,
    ITigoSalesProspectLead,
} from "@modules/tigoSalesFacade/interfaces/models/ITigoSalesProspectLead";
import { EAddressTypeValues } from "@modules/tigoSalesFacade/enums/EAddressTypeValues";
import { ISlot } from "@modules/tigoSalesFacade/interfaces/responses/ITigoTimeIntervalResponse";
import { getProspectLeadInfoAll, patchProspectLeadEntityInfo } from "@modules/tigoSalesFacade/apis/v1/prospectLead";
import { EEntityOptionsValues } from "@modules/tigoSalesFacade/enums/EEntityOptionsValues";
// eslint-disable-next-line import/no-duplicates
import { format, parseISO } from "date-fns";
import { APPOINTMENT_DATE_FORMAT } from "@constants";
// eslint-disable-next-line import/no-duplicates
import { es } from "date-fns/locale";
import { useHistory } from "react-router-dom";
import { FieldServiceConfirmAppointment } from "@modules/tigoSalesFacade/interfaces/payloads/ITigoFieldServiceConfirmAppointment";
import { processTaskEx } from "@modules/tigoSalesFacade/apis/v1/field-service";
import { FieldServiceConfirmAppointmentWFE } from "@modules/tigoSalesFacade/interfaces/payloads/ITigoFieldServiceConfirmAppointmentWFE";
import { processTasKWFE } from "@modules/tigoSalesFacade/apis/v1/confirmAppointment";
import { address } from "@translations/es/translation";

interface IParams {
    nextStepUrl?: string;
    prevStepUrl?: string;
    prospect: IProspect;
    customerInfo: ITigoSalesProspectLead;
    setCustomerInfo: KeyedMutator<ITigoSalesProspectLead>;
}

export const useScheduleInstallationStep = (props: IParams) => {
    const { customerInfo, setCustomerInfo, prospect, nextStepUrl } = props;
    const { t } = useTranslation(["common", "validation", "address", "appointment"]);

    const [isOpen, setIsOpen] = useState(false);
    const [isConfirmed, setIsConfirmed] = useState(false);
    const [modalTitle, setModalTitle] = useState("");
    const [provinceStr, setProvinceStr] = useState<string>("");
    const [districtStr, setDistrictStr] = useState<string>("");
    const [townshipStr, setTownshipStr] = useState<string>("");
    const [neighborhoodStr, setNeighborhoodStr] = useState<string>("");
    const [streetStr, setStreetStr] = useState<string>("");
    const [houseStr, setHouseStr] = useState<string>("");

    const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
    const { startFetching, endFetchingError, endFetching, isLoading } = useFetchState();
    const [availableSlots, setAvailableSlots] = useState<ISlot[]>();
    const [selectedSlot, setSelectedSlot] = useState<ISlot>();
    const [isErrorModalOpen, setIsErrorModalOpen] = useState(false);
    const [errorModalMessage, setErrorModalMessage] = useState("");
    const { push } = useHistory();

    const [installationAddress, setInstallationAddress] = useState<ILeadAddress | undefined>();

    useEffect(() => {
        if (customerInfo.customer?.appointmentEndDate && customerInfo.customer.appointmentStartDate) {
            setSelectedSlot({
                finish: customerInfo.customer.appointmentEndDate,
                grade: customerInfo.customer.rating ?? 0,
                start: customerInfo.customer.appointmentStartDate,
            });
        }
    }, [customerInfo]);

    useEffect(() => {
        const fetchProspectData = async () => {
            try {
                startFetching();
                const prospectData: ITigoSalesProspectLead = await getProspectLeadInfoAll(prospect.reference);

                const address = prospectData.address?.find(
                    (addr) => addr.addressType === EAddressTypeValues.INSTALLATION
                );

                setInstallationAddress(address);

                setProvinceStr(address?.province ?? "");
                setDistrictStr(address?.district ?? "");
                setTownshipStr(address?.subDistrict ?? "");
                setNeighborhoodStr(address?.neighborhood ?? "");
                setStreetStr(address?.street ?? "");
                setHouseStr(address?.house ?? "");

                endFetching();
            } catch (error) {
                endFetchingError(error);
            }
        };

        fetchProspectData();
    }, [prospect.reference]);

    const procesarSlot = (slot: ISlot) => {
        const titulo = format(parseISO(slot.start), APPOINTMENT_DATE_FORMAT, { locale: es });
        const startHour = parseISO(slot.start).getUTCHours();
        const time_slot = startHour === 8 ? "AM" : "PM";
        const jornada = startHour === 8 ? t("appointment:morning") : t("appointment:afternoon");
        const horario = startHour === 8 ? t("appointment:amTime") : t("appointment:pmTime");

        return {
            titulo,
            jornada,
            horario,
            time_slot,
        };
    };

    const enableNextStep = useCallback(() => {
        return isConfirmed && Boolean(selectedSlot);
    }, [isConfirmed, selectedSlot]);

    const handleNextStep = async () => {
        if (!isConfirmed || !selectedSlot) {
            return;
        }

        try {
            startFetching();
            const extraInfo = procesarSlot(selectedSlot);

            setCustomerInfo(
                async () => {
                    const customer: ILeadCustomer = {
                        ...customerInfo.customer,
                        appointmentStartDate: selectedSlot.start,
                        appointmentEndDate: selectedSlot.finish,
                        rating: selectedSlot.grade,
                        appointmentTimeSlot: extraInfo.time_slot,
                    };

                    await patchProspectLeadEntityInfo(prospect.reference, EEntityOptionsValues.CUSTOMER, customer);

                    return {
                        ...customerInfo,
                        customer: customer,
                    };
                },
                { revalidate: false }
            );

            endFetching();
            push(nextStepUrl ?? "");
        } catch (e) {
            endFetchingError(e);
        }
    };

    const handleOpenConfirmModal = () => {
        setModalTitle(t("appointment:confirmTitle"));
        setIsOpen(true);
    };
    const handleConfirmAppointment = async () => {
        setIsOpen(false);

        try {
            startFetching();

            const requestPayload: FieldServiceConfirmAppointment = {
                callId: customerInfo.customer?.callId,
                earlyStart: selectedSlot?.start,
                lateStart: selectedSlot?.finish,
                appointmentStart: selectedSlot?.start,
                appointmentFinish: selectedSlot?.finish,
                district: address?.district ?? "",
                street: address?.street ?? "",
                mcBillingAccountInfo: houseStr,
                mcContactEmail: customerInfo.customer?.contactEmail ?? "UNKNOWN_EMAIL",
                mcCustomerCode: customerInfo.customer?.documentId ?? "UNKNOWN_CUSTOMER_CODE",
                mcCustomerPhoneNumber: customerInfo.customer?.contactPhone
                    ? Number(customerInfo.customer.contactPhone)
                    : undefined,
                latitude: installationAddress?.latitude !== undefined && installationAddress?.latitude !== null
                    ? String(installationAddress.latitude)
                    : undefined,
                longitude: installationAddress?.longitude !== undefined && installationAddress?.longitude !== null
                    ? String(installationAddress.longitude)
                    : undefined,
                mcCustomerIdentityNumber: customerInfo.customer?.documentId ?? "UNKNOWN_ID",
                mcCustomerClass: "Residencial"
            };

            const response = await processTaskEx(requestPayload);

            if (response?.messageError && response.messageError !== "") {
                setErrorModalMessage(`DENEGADO: ${response.messageError}`);
                setIsErrorModalOpen(true);
                endFetching();
                return;
            }

            const orderId = prospect.reference;

            const payloadWFE: FieldServiceConfirmAppointmentWFE = {
                addressId: customerInfo.customer?.address ?? "",
                at: selectedSlot?.start || "", 
                contactUuid: customerInfo.customer?.contactPhone ?? "",
                correlationId: Number(new Date().getTime()),
                duration: 30,
                reasonCode: "INSTALACION_HFC",
                source: "TIGO_WEB",
                timeSlotCode: selectedSlot?.grade?.toString() ?? "",
            };
    

            await processTasKWFE(orderId, payloadWFE).catch((error) => {
                console.warn("Proceso WFE fallido:", error);
            });

            setIsConfirmed(true);
            setIsOpen(false);
            setIsSuccessModalOpen(true); 
            endFetching();
            //alert(t("appointment:confirmSuccess"));

        } catch (error) {
            setErrorModalMessage(`${t("appointment:confirmError")}: ${error.message}`);
            setIsErrorModalOpen(true);
        }
    };

    const handleErrorModalConfirm = () => {
        setIsErrorModalOpen(false);
        setTimeout(() => {
            window.location.reload();
        }, 4000);
    };

    const handleSlotSelection = (slot: ISlot) => {
        setSelectedSlot(slot);
        setIsConfirmed(false);
        handleOpenConfirmModal();
    };

    // Devuelve los valores correctos para departmentStr y municipalityStr
    return {
        isLoading,
        procesarSlot,
        enableNextStep,
        handleNextStep,

        availableSlots,
        setAvailableSlots,

        selectedSlot,
        setSelectedSlot,

        departmentStr: "", 
        municipalityStr: "", 

        handleOpenConfirmModal,
        handleConfirmAppointment,

        modalTitle,
        isOpen,
        setIsOpen,
        handleSlotSelection,
        isConfirmed,

        provinceStr,
        districtStr,
        townshipStr,
        neighborhoodStr,
        streetStr,
        houseStr,

        isErrorModalOpen,
        errorModalMessage,
        handleErrorModalConfirm,

        isSuccessModalOpen,
        setIsSuccessModalOpen,
    };
};
