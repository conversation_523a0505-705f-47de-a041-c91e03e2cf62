import { SectionCard, useOutlinedCardStyle, useSectionIconStyle } from "@common";
import { <PERSON><PERSON>, Card, CardContent, SvgIcon } from "@mui/material";
import { useTranslation } from "react-i18next";
import SchedulesTable, { IAppointmentSchedule } from "../SubscriptionsAddOns/Components/SchedulesTable";
import { ReactComponent as ScheduleIcon } from "@static/icons/calendar_outlined.svg";
import { useInformationSchedule } from "./useInformationSchedule";
import { TContactDetails } from "../CustomerDetails/ICustomerDetails";
import ScheduleAppointment from "./ScheduleAppointment/ScheduleAppointment";
import ReschedulceAppointment from "./ScheduleAppointment/ReschedulceAppointment";
import { IAccountDetails } from "../CustomerPanel/ICustomer";
import { IFetchedData } from "itsf-ui-common";
import { useState } from "react";

interface IPropsInformationSchedule {
    accountId: string;
    contactDetails: TContactDetails | undefined;
    account: IFetchedData<IAccountDetails | undefined>;
}

const InformationSchedule = ({ accountId, contactDetails }: IPropsInformationSchedule) => {
    const { t } = useTranslation(["customer", "common"]);
    const [showRescheduleForm, setShowRescheduleForm] = useState(false);
    const [selectedAppointment, setSelectedAppointment] = useState<IAppointmentSchedule | null>(null);
    const { classes: outlinedCardClasses } = useOutlinedCardStyle();
    const { classes: iconClasses } = useSectionIconStyle();

    const {
        appointments,
        addNewSchedule,
        setAddNewSchedule,
        availableSlots,
        callId,
        procesarSlot,
        selectedSlot,
        setAvailableSlots,
        setSelectedSlot,
        setShowNewSchedule,
        showNewScheledule,
    } = useInformationSchedule({ accountId, contactDetails });

    const filterContact = contactDetails?.contactAddresses.find((address) => address.type === "BILLING");
    const filterAddress = contactDetails?.contactAddresses.find((address) => address.type === "INSTALLATION");


    const handleReschedule = (appointment: IAppointmentSchedule) => {
    setSelectedAppointment(appointment);
    setShowRescheduleForm(true);
    };

    // Maneja el cierre del formulario de reagendamiento
    const handleCloseRescheduleForm = () => {
        setShowRescheduleForm(false);
        setSelectedAppointment(null);

    };

    

    return (
        <div>
            <Card className={outlinedCardClasses.main} elevation={0} variant="outlined">
                <CardContent>
                    <SectionCard
                        icon={<SvgIcon className={iconClasses.main} component={ScheduleIcon} viewBox="0 0 21 19" />}
                        title={t("customer:agendaTitle")}
                        actionButtons={
                            (!addNewSchedule && !showNewScheledule && !showRescheduleForm) ? (
                                <Button color="primary" variant="contained" onClick={() => setAddNewSchedule(true)}>
                                    {t("common:add")}
                                </Button>
                            ) : undefined
                        }
                    >
                        {addNewSchedule || showNewScheledule ? (
                            <ScheduleAppointment
                                availableSlots={availableSlots}
                                callId={callId ?? ""}
                                filterContact={filterContact}
                                procesarSlot={procesarSlot}
                                selectedSlot={selectedSlot}
                                setAddNewSchedule={setAddNewSchedule}
                                setAvailableSlots={setAvailableSlots}
                                setSelectedSlot={setSelectedSlot}
                                setShowNewSchedule={setShowNewSchedule}
                                contactDetails={contactDetails}
                            />
                        ) : showRescheduleForm ? (
                            <ReschedulceAppointment
                                availableSlots={availableSlots}
                                callId={callId ?? ""}
                                filterAddress={filterAddress}
                                procesarSlot={procesarSlot}
                                selectedSlot={selectedSlot}
                                setAvailableSlots={setAvailableSlots}
                                setSelectedSlot={setSelectedSlot}
                                contactDetails={contactDetails}
                                oldAppointment={selectedAppointment}
                                onCancel={handleCloseRescheduleForm}
                            />
                        ) : (
                            <SchedulesTable
                                appointments={appointments}
                                contactDetails={contactDetails}
                                onReschedule={handleReschedule}
                            />
                        )}
                    </SectionCard>
                </CardContent>
            </Card>
        </div>
    );
};

export default InformationSchedule;