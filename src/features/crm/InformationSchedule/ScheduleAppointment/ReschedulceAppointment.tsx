import ScheduleForm from "@common/ScheduleForm/ScheduleForm";
import { Grid, Button, TextField } from "@mui/material";
import { Dispatch, useState } from "react";
import { ISlot } from "@modules/tigoSalesFacade/interfaces/responses/ITigoTimeIntervalResponse";
import { IAddress } from "@common/Address/IAddress";
import ReScheduleConfirmationModal from "@common/styleComponents/OrderConfirmationModal/ReScheduleConfirmationModal";
import { useTranslation } from "react-i18next";
import { processTaskEx } from "@modules/tigoSalesFacade/apis/v1/field-service";
import { FieldServiceConfirmAppointment } from "@modules/tigoSalesFacade/interfaces/payloads/ITigoFieldServiceConfirmAppointment";
import { ConfirmationModal, useSnackBar } from "@common";
import { useFetchState } from "@hooks/useFetchState";
import { FieldServiceConfirmAppointmentWFE } from "@modules/tigoSalesFacade/interfaces/payloads/ITigoFieldServiceConfirmAppointmentWFE";
import { processTasKWFE } from "@modules/tigoSalesFacade/apis/v1/confirmAppointment";
import { TContactDetails } from "@features/crm/CustomerDetails/ICustomerDetails";
import { IAppointmentSchedule } from "@features/crm/SubscriptionsAddOns/Components/SchedulesTable";
import { IAppointmentRequest, UpdateAppointment } from "@common/Appointment/AppointmentAPI";

interface IScheduleApppointmentProps {
    availableSlots: ISlot[] | undefined;
    callId: string;
    filterAddress: IAddress | undefined;
    contactDetails?: TContactDetails | undefined;
    procesarSlot: (slot: ISlot) => {
        titulo: string;
        jornada: string;
        horario: string;
        time_slot: string;
    };
    selectedSlot: ISlot | undefined;
    setAvailableSlots: Dispatch<React.SetStateAction<ISlot[] | undefined>>;
    setSelectedSlot: (slot: ISlot | undefined) => void;
    oldAppointment: IAppointmentSchedule;
    onCancel?: () => void;
}

const TASK_TYPE_NEW_RESCHEDULE = process.env.TASK_TYPE_NEW_RESCHEDULE || "";

const ReschedulceAppointment = ({
    availableSlots,
    callId,
    filterAddress,
    procesarSlot,
    selectedSlot,
    setAvailableSlots,
    setSelectedSlot,
    contactDetails,
    oldAppointment,
    onCancel
}: IScheduleApppointmentProps) => {
    const { t } = useTranslation(["acquisition", "appointment", "address", "common"]);
    const { setSnackBarSuccess, setSnackBarError } = useSnackBar();
    const { startFetching, endFetching, endFetchingError } = useFetchState();

    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
    const [tempSlotInfo, setTempSlotInfo] = useState<ReturnType<typeof procesarSlot> | null>(null);


    debugger
    const handleProcessTaskEx = async () => {
        if (selectedSlot?.start && selectedSlot.finish) {
            try {
                startFetching();

                const payload: FieldServiceConfirmAppointment = {
                    earlyStart: selectedSlot.start,
                    lateStart: selectedSlot.finish,
                    appointmentStart: selectedSlot.start,
                    appointmentFinish: selectedSlot.finish,
                    callId: oldAppointment.callId,
                    taskType: TASK_TYPE_NEW_RESCHEDULE,
                    area: filterAddress?.area,
                };

                const response = await processTaskEx(payload);

                if (response?.messageError && response.messageError !== "") {
                    setSnackBarError(`DENEGADO: ${response.messageError}`);
                    endFetching();
                    return;
                }

                const payloadWFE: FieldServiceConfirmAppointmentWFE = {
                    addressId: filterAddress?.addressLine1 ?? "UNKNOWN_ADDRESS",
                    at: selectedSlot?.start,
                    contactUuid: contactDetails?.contactUuid ?? "UNKNOWN_CONTACT",
                    correlationId: Number(new Date().getTime()),
                    duration: 30,
                    reasonCode: "Daño de equipo",
                    source: "TIGO_WEB",
                    timeSlotCode: selectedSlot?.grade?.toString() ?? "UNKNOWN_SLOT",
                };


                const timeSlot =
                    selectedSlot?.start && new Date(selectedSlot.start).getUTCHours() >= 12 ? "PM" : "AM";

                const appointmentRequest: Partial<IAppointmentRequest> = {
                    at: selectedSlot?.start ?? "",
                    timeSlot: { code: timeSlot },
                };

                await UpdateAppointment(oldAppointment.id, appointmentRequest);


                await processTasKWFE(callId, payloadWFE).catch((error) => {
                    console.warn("Proceso WFE fallido:", error);
                });

                setSnackBarSuccess(t("common:rescheduleSuccesfullMessage"));
                setIsModalOpen(true);
                endFetching();
            } catch (error: any) {
                endFetchingError(error);
                setSnackBarError(`${t("appointment:confirmError")}: ${error.message}`);
            }
        }
    };

    const handleCancelProcess = () => {
        setSelectedSlot(undefined);
        setAvailableSlots([]);
        if (onCancel) onCancel();
    };

    const handleCloseModal = () => {
        setIsModalOpen(false);
        setSelectedSlot(undefined);
        setAvailableSlots([]);
    };

    const handleSlotSelection = (slot: ISlot) => {
        const parsed = procesarSlot(slot);
        setTempSlotInfo(parsed);
        setSelectedSlot(slot);
        setIsConfirmModalOpen(true);
    };

    const handleConfirmSlot = () => {
        handleProcessTaskEx();
        setIsConfirmModalOpen(false);
    };

    const handleCloseConfirmModal = () => {
        setTempSlotInfo(null);
        setIsConfirmModalOpen(false);
    };

    return (
        <Grid container direction={"row"} spacing={2} width={"100%"}>
            <Grid item xs={12}>
                <ScheduleForm
                    availableSlots={availableSlots}
                    callId={callId ?? "NSC18042024CO0060"}
                    fullWidth
                    installationAddress={filterAddress}
                    municipalityStr={filterAddress?.street ?? ""}
                    districtStr={filterAddress?.town ?? ""}
                    townshipStr={filterAddress?.addressLine1 ?? ""}
                    neighborhoodStr={filterAddress?.area ?? ""}
                    streetStr={filterAddress?.street ?? ""}
                    houseStr={filterAddress?.addressLine3 ?? ""}
                    provinceStr={filterAddress?.addressLine2 ?? ""}
                    procesarSlot={procesarSlot}
                    selectedSlot={selectedSlot}
                    setAvailableSlots={setAvailableSlots}
                    setSelectedSlot={selectedSlot}
                    handleSlotSelection={handleSlotSelection}
                    modalTitle={t("appointment:confirmTitle")}
                    isOpen={isConfirmModalOpen}
                    setIsOpen={setIsConfirmModalOpen}
                    handleConfirmAppointment={handleConfirmSlot}
                    taskType={TASK_TYPE_NEW_RESCHEDULE}
                />
                <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                    <Button color="secondary" variant="contained" onClick={handleCancelProcess}>
                        {t("common:cancel")}
                    </Button>
                </div>
            </Grid>

            <ConfirmationModal
                open={isConfirmModalOpen}
                title={t("appointment:confirmTitle")}
                handleClose={handleCloseConfirmModal}
                handleConfirm={handleConfirmSlot}
            >
                <p>{t("appointment:confirmMessage")}</p>
                {tempSlotInfo && (
                    <Grid container spacing={2} mt={2}>
                        <Grid item md={6} xs={12}>
                            <TextField
                                disabled
                                fullWidth
                                label={t("appointment:detailAppointment.date")}
                                value={tempSlotInfo.titulo}
                            />
                        </Grid>
                        <Grid item md={6} xs={12}>
                            <TextField disabled fullWidth label={tempSlotInfo.jornada} value={tempSlotInfo.horario} />
                        </Grid>
                    </Grid>
                )}
            </ConfirmationModal>

            <ReScheduleConfirmationModal
                isOpen={isModalOpen}
                message={t("common:rescheduleSuccesfullMessage")}
                onClose={handleCloseModal}
            />
        </Grid>
    );
};

export default ReschedulceAppointment;
