import { IAddress } from "@common/Address/IAddress";
import ScheduleForm from "@common/ScheduleForm/ScheduleForm";
import { ISlot } from "@modules/tigoSalesFacade/interfaces/responses/ITigoTimeIntervalResponse";
import { <PERSON><PERSON>, Divider, Grid, TextField } from "@mui/material";
import { Dispatch, useState } from "react";
import { useScheduleAppointment } from "./useScheduleAppointment";
import { useFetchState } from "@hooks/useFetchState";
import { ConfirmationModal, useSnackBar } from "@common";
import ReScheduleConfirmationModal from "@common/styleComponents/OrderConfirmationModal/ReScheduleConfirmationModal";
import { processTaskEx } from "@modules/tigoSalesFacade/apis/v1/field-service";
import { FieldServiceConfirmAppointment } from "@modules/tigoSalesFacade/interfaces/payloads/ITigoFieldServiceConfirmAppointment";
import { useTranslation } from "react-i18next";
import { FieldServiceConfirmAppointmentWFE } from "@modules/tigoSalesFacade/interfaces/payloads/ITigoFieldServiceConfirmAppointmentWFE";
import { processTasKWFE } from "@modules/tigoSalesFacade/apis/v1/confirmAppointment";
import { TContactDetails } from "@features/crm/CustomerDetails/ICustomerDetails";
import { CreateAppointment, IAppointmentRequest } from "@common/Appointment/AppointmentAPI";

interface IScheduleApppointmentProps {
    availableSlots: ISlot[] | undefined;
    callId: string;
    filterContact: IAddress | undefined;
    contactDetails?: TContactDetails | undefined;
    procesarSlot: (slot: ISlot) => {
        titulo: string;
        jornada: string;
        horario: string;
        time_slot: string;
    };
    selectedSlot: ISlot | undefined;
    setAvailableSlots: Dispatch<React.SetStateAction<ISlot[] | undefined>>;
    setSelectedSlot: (slot: ISlot) => void;
    setAddNewSchedule: (value: boolean) => void;
    setShowNewSchedule: (value: boolean) => void;
}

const TASK_TYPE_NEW_SCHEDULE = process.env.REACT_APP_TASK_TYPE_NEW_SCHEDULE || "";

const ScheduleAppointment = ({
    availableSlots,
    callId,
    filterContact,
    procesarSlot,
    selectedSlot,
    setAvailableSlots,
    setSelectedSlot,
    setAddNewSchedule,
    setShowNewSchedule,
    contactDetails
}: IScheduleApppointmentProps) => {
    const { t } = useTranslation(["acquisition", "appointment", "address", "common", "customer"]);
    const { verifyUserCase, userIsVerified } = useScheduleAppointment();

    const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
    const [tempSlotInfo, setTempSlotInfo] = useState<ReturnType<typeof procesarSlot> | null>(null);
    const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);

    const { setSnackBarSuccess, setSnackBarError } = useSnackBar();
    const { startFetching, endFetching, endFetchingError } = useFetchState();

    const handleSlotSelection = (slot: ISlot) => {
        setTempSlotInfo(procesarSlot(slot));
        setSelectedSlot(slot);
        setIsConfirmModalOpen(true);
    };

    const handleCloseConfirmModal = () => {
        setIsConfirmModalOpen(false);
        setTempSlotInfo(null);
    };

    const handleConfirmSlot = async () => {
        if (!selectedSlot?.start || !selectedSlot?.finish) return;

        setIsConfirmModalOpen(false);
        try {
            startFetching();
            const payload: FieldServiceConfirmAppointment = {
                callId,
                earlyStart: selectedSlot.start,
                lateStart: selectedSlot.finish,
                appointmentStart: selectedSlot.start,
                appointmentFinish: selectedSlot.finish,
            };

            const response = await processTaskEx(payload);

            if (response?.messageError && response.messageError !== "") {
                setSnackBarError(`DENEGADO: ${response.messageError}`);
                endFetching();
                return;
            }

            

            const payloadWFE: FieldServiceConfirmAppointmentWFE = {
                addressId: filterContact?.addressLine1 ?? "UNKNOWN_ADDRESS",
                at: selectedSlot?.start,
                contactUuid: contactDetails?.contactUuid ?? "UNKNOWN_CONTACT",
                correlationId: Number(new Date().getTime()),
                duration: 30,
                reasonCode: "INSTALACION_HFC",
                source: "TIGO_WEB",
                timeSlotCode: selectedSlot?.start ?? "UNKNOWN_SLOT",
            };

             const appointmentRequest: IAppointmentRequest = {
                    addressId: filterContact?.id ?? 0,
                    at: selectedSlot?.start ?? "",
                    contactUuid: contactDetails?.contactUuid ?? "",
                    correlationId: Number(new Date().getTime()),
                    duration: 240,
                    reasonCode: "FIX",
                    timeSlotCode: selectedSlot?.start,
                    source: "FIELD-SERVICE",
                };

            await CreateAppointment(appointmentRequest);

            await processTasKWFE(callId, payloadWFE).catch((error) => {
                console.warn("Proceso WFE fallido:", error);
            });

            setSnackBarSuccess(t("appointment:confirmSuccess"));
            endFetching();
            setAddNewSchedule(false);
            setShowNewSchedule(true);
            setIsSuccessModalOpen(true); // Mostrar modal de éxito
        } catch (err: any) {
            endFetchingError(err);
            setSnackBarError(`${t("appointment:confirmError")}: ${err.message}`);
        }
    };

    return (
        <>
            <Grid container direction={"row"} spacing={2} width={"100%"}>
                <Grid item xs={12}>
                    <Divider textAlign="center">{t("customer:numberOfCase")}</Divider>
                </Grid>
                <Grid item xs={4}>
                    <TextField fullWidth label={"No. de Caso"} />
                </Grid>
                <Grid item xs={2}>
                    <Button color="primary" variant="outlined" onClick={() => verifyUserCase("222")}>
                        {t("common:verify")}
                    </Button>
                </Grid>

                {userIsVerified?.status?.name === "open" && (
                    <Grid item xs={12}>
                        <ScheduleForm
                            availableSlots={availableSlots}
                            callId={callId ?? ""}
                            fullWidth
                            municipalityStr={filterContact?.street ?? ""}
                            districtStr={filterContact?.town ?? ""}
                            townshipStr={filterContact?.addressLine1 ?? ""}
                            neighborhoodStr={filterContact?.area ?? ""}
                            streetStr={filterContact?.street ?? ""}
                            houseStr={filterContact?.addressLine3 ?? ""}
                            provinceStr={filterContact?.addressLine2 ?? ""}
                            procesarSlot={procesarSlot}
                            selectedSlot={selectedSlot}
                            setAvailableSlots={setAvailableSlots}
                            setSelectedSlot={selectedSlot}
                            handleSlotSelection={handleSlotSelection}
                            modalTitle={t("appointment:confirmTitle")}
                            isOpen={isConfirmModalOpen}
                            setIsOpen={setIsConfirmModalOpen}
                            handleConfirmAppointment={handleConfirmSlot}
                            taskType={TASK_TYPE_NEW_SCHEDULE}
                        />
                        <Button
                            color="secondary"
                            variant="contained"
                            onClick={() => {
                                setAddNewSchedule(false);
                                setShowNewSchedule(false);
                            }}
                        >
                            {t("common:cancel")}
                        </Button>
                    </Grid>
                )}
            </Grid>

            <ConfirmationModal
                open={isConfirmModalOpen}
                title={t("appointment:confirmTitle")}
                handleClose={handleCloseConfirmModal}
                handleConfirm={handleConfirmSlot}
            >
                <p>{t("appointment:confirmMessage")}</p>
                {tempSlotInfo && (
                    <Grid container marginTop={2} spacing={2}>
                        <Grid item md={6} xs={12}>
                            <TextField
                                disabled
                                fullWidth
                                label={t("appointment:detailAppointment.date")}
                                value={tempSlotInfo.titulo}
                            />
                        </Grid>
                        <Grid item md={6} xs={12}>
                            <TextField
                                disabled
                                fullWidth
                                label={tempSlotInfo.jornada}
                                value={tempSlotInfo.horario}
                            />
                        </Grid>
                    </Grid>
                )}
            </ConfirmationModal>
            <ReScheduleConfirmationModal
                isOpen={isSuccessModalOpen}
                message={t("common:scheduleSuccesfullMessage", { returnObjects: false })}
                onClose={() => setIsSuccessModalOpen(false)}
            />
        </>
    );
};
export default ScheduleAppointment;
