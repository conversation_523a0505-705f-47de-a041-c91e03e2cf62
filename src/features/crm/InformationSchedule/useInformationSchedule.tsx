import { useEffect, useState } from "react";
import { IAppointmentSchedule } from "../SubscriptionsAddOns/Components/SchedulesTable";
import { IAppointmentResponse, GetAppointments } from "@common/Appointment/AppointmentAPI";
import { GetTask } from "@common/Task/getTask";
import { TContactDetails } from "../CustomerDetails/ICustomerDetails";
import { useTranslation } from "react-i18next";
// eslint-disable-next-line import/no-duplicates
import { format, parseISO } from "date-fns";
import { APPOINTMENT_DATE_FORMAT } from "@constants";
// eslint-disable-next-line import/no-duplicates
import { es } from "date-fns/locale";
import { useFetchState } from "@hooks/useFetchState";
import { ISlot } from "@modules/tigoSalesFacade/interfaces/responses/ITigoTimeIntervalResponse";
import { getUniqueService } from "@services/subscription/accountId/api/getSubscriptionsAccountIds";
import { ITaskSummary } from "@common/Appointment/IprocessTask";

interface IUseInformationSchedule {
    contactDetails?: TContactDetails | undefined;
    accountId: string;
}

export const useInformationSchedule = ({ accountId }: IUseInformationSchedule) => {
    const { t } = useTranslation(["customer", "appointment"]);
    const { endFetchingError } = useFetchState();
    // hooks for appointments
    const [appointments, setAppointments] = useState<IAppointmentSchedule[]>([]);
    const [addNewSchedule, setAddNewSchedule] = useState<boolean>(false);

    // hooks for schedule installation
    const [isSuccessCallId, setIsSuccessCallId] = useState<boolean>(false);
    const [callId, setCallId] = useState<string>("");
    const [availableSlots, setAvailableSlots] = useState<ISlot[]>();
    const [selectedSlot, setSelectedSlot] = useState<ISlot | undefined>(undefined);
    const [showNewScheledule, setShowNewSchedule] = useState<boolean>(false);

    const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
    const [tempSlotInfo, setTempSlotInfo] = useState<ReturnType<typeof procesarSlot> | null>(null);

    const handleSlotSelection = (slot: ISlot) => {
        const parsedSlot = procesarSlot(slot);
        setTempSlotInfo(parsedSlot);
        setSelectedSlot(slot);
        setIsConfirmModalOpen(true);
    };
    const handleCloseConfirmModal = () => {
        setTempSlotInfo(null);
        setIsConfirmModalOpen(false);
    };
    const handleConfirmAppointmentSlot = () => {
        setIsConfirmModalOpen(false);
    };
    const uniqueService = async () => {
        try {
            const response = await getUniqueService({});
            setCallId(response.reference);
            setIsSuccessCallId(true);
        } catch (error) {
            endFetchingError(error);
        }
    };
    const procesarSlot = (slot: ISlot) => {
        const titulo = format(parseISO(slot.start), APPOINTMENT_DATE_FORMAT, { locale: es });
        const startHour = parseISO(slot.start).getUTCHours();
        const time_slot = startHour === 8 ? "AM" : "PM";
        const jornada = startHour === 8 ? t("appointment:morning") : t("appointment:afternoon");
        const horario = startHour === 8 ? t("appointment:amTime") : t("appointment:pmTime");

        return {
            titulo,
            jornada,
            horario,
            time_slot,
        };
    };

    useEffect(() => {
        const uniqueCallId = localStorage.getItem("UNIQUE_CALL_ID");
        if (!isSuccessCallId && !uniqueCallId) {
            uniqueService();
        }

        if (uniqueCallId) {
            setCallId(uniqueCallId);
        }
    }, [isSuccessCallId]);

    useEffect(() => {
        if (callId) {
            localStorage.setItem("UNIQUE_CALL_ID", callId);
        }
    }, [callId]);

    const fetchData = async () => {
        try {
            // MOCK: Datos de appointments
            /*  const mockAppointments = [
                  {
                      id: "MOCK_CALL_ID_1",
                      at: "2025-06-11T08:00:00.000Z",
                      reason: "Instalación de servicio",
                      callId: "MOCK_CALL_ID_1",
                      status: "CONFIRMED",
                      addressId: "ADDR_001",
                  },
                  {
                      id: "MOCK_CALL_ID_2",
                      at: "2025-06-12T14:00:00.000Z",
                      reason: "Reparación",
                      callId: "MOCK_CALL_ID_2",
                      status: "PENDING",
                      addressId: "ADDR_002",
                  },
              ];
              setAppointments(mockAppointments);
  
              // MOCK: Simulación de actualización con un "task"
              const mockTaskData = {
                  callId: "MOCK_CALL_ID_1",
                  status: "COMPLETED",
                  mcConnectionData: "ADDR_001",
              };
  
              setAppointments((prevAppointments) =>
                  prevAppointments.map((appointment) =>
                      appointment.id === mockTaskData.callId
                          ? {
                              ...appointment,
                              callId: mockTaskData.callId,
                              status: mockTaskData.status,
                              addressId: mockTaskData.mcConnectionData,
                          }
                          : appointment
                  )
              );
  */

            const data: IAppointmentResponse = await GetAppointments(accountId);
            if (data.status === 200 && data.data) {
                const initialAppointments = data.data.map((item: any) => ({
                    id: item.id?.toString() ?? "",
                    at: item.at ?? "",
                    reason: item.reason ?? "",
                    callId: item.correlationId ?? "",
                    status: item.status ?? "",
                    addressId: item.addressId?.toString() ?? "",
                }));
                setAppointments(initialAppointments);
            }

            //const taskData = await GetTask("NSC18042024CO0045");

            const taskData: ITaskSummary = {
                callId: "NSC18042024CO0045",
                mcConnectionData: "ADDR_001",
                status: "COMPLETED",
            };

            setAppointments((prevAppointments) =>
                prevAppointments.map((appointment) =>
                    appointment.id === taskData.callId
                        ? {
                            ...appointment,
                            callId: taskData.callId,
                            status: taskData.status,
                            addressId: taskData.mcConnectionData,
                        }
                        : appointment
                )
            );

        } catch (error) {
            throw new Error("Error al obtener el appointment");
        }
    };

    useEffect(() => {
        fetchData();
    }, [accountId]);

    return {
        appointments,
        setAddNewSchedule,
        addNewSchedule,
        procesarSlot,
        callId,
        availableSlots,
        setAvailableSlots,
        selectedSlot,
        setSelectedSlot,
        setShowNewSchedule,
        showNewScheledule,

        handleSlotSelection,
        isConfirmModalOpen,
        handleCloseConfirmModal,
        handleConfirmAppointmentSlot,
        tempSlotInfo,
    };
};
