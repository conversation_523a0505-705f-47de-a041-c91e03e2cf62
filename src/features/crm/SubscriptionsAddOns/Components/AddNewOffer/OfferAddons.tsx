import { Box, Fab, Grid, Typography } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import { IAddon } from "@services/subscription/accountId/interface/IAddOns";
import { centsToPrice } from "itsf-ui-common";
interface IProps {
    addon: IAddon;
    isAdded: boolean;
    handleRemoveClick: () => void;
    handleAddClick: () => void;
    key: string;
    quantity: number;
    disabled: boolean;
}

const OfferAddons = ({ key, addon, isAdded, handleAddClick, handleRemoveClick, quantity, disabled }: IProps) => {
    return (
        <Grid item key={key} xs={12}>
            <Box
                sx={{
                    border: "1px solid #A0EAFF",
                    padding: 2,
                    backgroundColor: disabled ? "#dedede" : isAdded ? "#0086ff" : "transparent",
                }}
            >
                <Grid alignItems="center" container justifyContent="space-between">
                    <Grid item xs={6}>
                        <Box>
                            <Typography
                                fontWeight={800}
                                sx={{ color: disabled ? "#565656" : isAdded ? "#ebebeb" : "#1b60ec" }}
                                variant="subtitle1"
                            >
                                {addon.description}
                            </Typography>
                        </Box>
                    </Grid>
                    <Grid container item justifyContent="center" xs={2}>
                        <Box>
                            <Typography sx={{ color: isAdded ? "#ebebeb" : "#565656" }}>
                                ${centsToPrice(Number(addon.prices[0]?.price ?? 0))}
                            </Typography>
                        </Box>
                    </Grid>
                    <Grid item xs={4}>
                        <Grid alignItems="center" container justifyContent="flex-end">
                            <Fab
                                aria-label="remove-offer"
                                color="primary"
                                disabled={disabled}
                                size="small"
                                sx={{ marginRight: 1 }}
                                onClick={handleRemoveClick}
                            >
                                <RemoveIcon />
                            </Fab>
                            <Box sx={{ display: "flex", alignItems: "center", justifyContent: "center", width: 30 }}>
                                <Typography sx={{ color: isAdded ? "#ebebeb" : "#565656" }}>{quantity}</Typography>
                            </Box>
                            <Fab
                                aria-label="add-offer"
                                color="primary"
                                disabled={disabled || addon.maxQuantity === quantity}
                                size="small"
                                onClick={handleAddClick}
                            >
                                <AddIcon />
                            </Fab>
                        </Grid>
                    </Grid>
                </Grid>
            </Box>
        </Grid>
    );
};

export default OfferAddons;
