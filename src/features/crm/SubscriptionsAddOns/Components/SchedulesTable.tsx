import { TContactDetails } from "@features/crm/CustomerDetails/ICustomerDetails";
import {
    Button,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
} from "@mui/material";
import { useTranslation } from "react-i18next";

export interface IAppointmentSchedule {
    id: string;
    at: string;
    status: string;
    reason: string;
    addressId: string;

    callId: string;
    taskType: string;
    area: string;
}

interface IParams {
    appointments: IAppointmentSchedule[];
    contactDetails: TContactDetails | undefined;
    onReschedule: (appointment: IAppointmentSchedule) => void;
}

const SchedulesTable = ({ appointments, contactDetails, onReschedule }: IParams) => {
    const { t } = useTranslation(["customer", "common"]);

    return (
        <div>
            <Paper elevation={1}>
                <TableContainer component="div">
                    <Table aria-label="custom table" size="small" sx={{ minWidth: 650 }}>
                        <TableHead>
                            <TableRow>
                                <TableCell>{t("customer:agendaId")}</TableCell>
                                <TableCell>{t("customer:date")}</TableCell>
                                <TableCell>{t("customer:state")}</TableCell>
                                <TableCell>{t("customer:typeOfAgenda")}</TableCell>
                                <TableCell>{t("customer:address")}</TableCell>
                                <TableCell>{t("customer:appointmentAction")}</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {appointments.length === 0 ? (
                                <TableRow>
                                    <TableCell align="center" colSpan={10}>
                                        <Typography>{t("customer:noDataAvailable")}</Typography>
                                    </TableCell>
                                </TableRow>
                            ) : (
                                appointments.map((appointment) => (
                                    <TableRow key={appointment.id}>
                                        <TableCell sx={{ fontSize: "0.7rem" }}>{appointment.id}</TableCell>
                                        <TableCell sx={{ fontSize: "0.7rem", whiteSpace: "nowrap" }}>
                                            {appointment.at}
                                        </TableCell>
                                        <TableCell sx={{ fontSize: "0.7rem", whiteSpace: "nowrap" }}>
                                            {appointment.status}
                                        </TableCell>
                                        <TableCell sx={{ fontSize: "0.7rem" }}>{appointment.reason}</TableCell>
                                        <TableCell sx={{ fontSize: "0.7rem" }}>{appointment.addressId}</TableCell>
                                        <TableCell>
                                            <Button
                                                color="primary"
                                                sx={{ fontSize: "0.75rem", textAlign: "center", whiteSpace: "nowrap" }}
                                                variant="contained"
                                                onClick={() => onReschedule(appointment)}
                                            >
                                                {t("customer:rescheduleLabel")}
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </TableContainer>
            </Paper>
        </div>
    );
};

export default SchedulesTable;
