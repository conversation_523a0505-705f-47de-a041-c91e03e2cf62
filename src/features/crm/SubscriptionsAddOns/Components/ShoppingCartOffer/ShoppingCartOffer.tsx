import { Box, Grid, Typography } from "@mui/material";
import { IAddedAddon } from "../AddNewOffer/IAddNewOffer";
import { useTranslation } from "react-i18next";
import { centsToPrice } from "itsf-ui-common";

interface IProps {
    addedAddons: IAddedAddon[];
    totalAddons: number;
}

const ShoppingCartOffer = ({ addedAddons, totalAddons }: IProps) => {
    const { t } = useTranslation(["customer"]);

    return (
        <Box sx={{ pb: 1, pt: 3 }}>
            <Typography color={"primary"} pb={3} textAlign={"center"} variant="h5">
                {t("customer:detailOffer")}
            </Typography>
            {addedAddons.map((item) => (
                <Grid container key={item.addon.code} spacing={3} sx={{ mb: 2 }}>
                    <Grid item xs={12}>
                        <Typography color={"primary"}>{item.offer.displayName}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                        <Typography>{item.addon.description}</Typography>
                    </Grid>
                    <Grid item xs={2}>
                        <Typography>{item.quantity}</Typography>
                    </Grid>
                    <Grid item xs={4}>
                        <Typography textAlign={"right"}>
                            ${item.quantity * Number(centsToPrice(item.addon.prices[0]?.price ?? 0))}
                        </Typography>
                    </Grid>
                </Grid>
            ))}
            <Box sx={{ marginTop: 2, backgroundColor: "primary.main", padding: 2, borderRadius: 1 }}>
                <Grid alignItems="center" container justifyContent="space-between">
                    <Grid item xs={6}>
                        <Typography color="white" textAlign="left" variant="h6">
                            {t("customer:monthlyAmount")}
                        </Typography>
                    </Grid>
                    <Grid item xs={6}>
                        <Typography color="white" textAlign="right" variant="h6">
                            ${centsToPrice(totalAddons)}
                        </Typography>
                    </Grid>
                </Grid>
            </Box>
        </Box>
    );
};

export default ShoppingCartOffer;
