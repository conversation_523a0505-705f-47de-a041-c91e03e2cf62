import { Dispatch, SetStateAction, useEffect, useState } from "react";
import { useSnackBar } from "@common";
import { useFetchState } from "@hooks/useFetchState";
import { Divider, Grid, TextField } from "@mui/material";
import { useScheduleInstallation } from "./useScheduleInstallation";
import { ScheduleSlotsForm } from "@features/acquisition/PostpayAcquisition/Postpay/common/ScheduleInstallationStep/ScheduleSlotsForm/ScheduleSlotsForm";
import { TContactDetails } from "@features/crm/CustomerDetails/ICustomerDetails";
import { CONTACT_ADDRES_TYPE_BILLING } from "@constants";
import { SlotsForm } from "@features/acquisition/PostpayAcquisition/Postpay/common/ScheduleInstallationStep/SlotsForm/SlotsForm";
import { ISlot } from "@modules/tigoSalesFacade/interfaces/responses/ITigoTimeIntervalResponse";
import { ConfirmationModal } from "@common";
import { FieldServiceConfirmAppointmentWFE } from "@modules/tigoSalesFacade/interfaces/payloads/ITigoFieldServiceConfirmAppointmentWFE";
import { processTaskEx } from "@modules/tigoSalesFacade/apis/v1/field-service";
import { FieldServiceConfirmAppointment } from "@modules/tigoSalesFacade/interfaces/payloads/ITigoFieldServiceConfirmAppointment";
import { processTasKWFE } from "@modules/tigoSalesFacade/apis/v1/confirmAppointment";
import { useTranslation } from "react-i18next";

interface IPropsScheduleInstallation {
    contact: TContactDetails | undefined;
    setSuccessScheduleInstallation: Dispatch<SetStateAction<boolean>>;
    setScheduleInstallation: Dispatch<SetStateAction<ISlot>>;
    setCorrelationId: Dispatch<SetStateAction<string>>;
    scheduleSuccess?: boolean;
}

const TASK_TYPE_ADDONS = process.env.TASK_TYPE_ADDONS || "Daño de Equipo";
const TASK_CATEGORY_ADDONS = process.env.TASK_CATEGORY_ADDONS || "Daño de Equipo";

const ScheduleInstallation = ({
    contact,
    setSuccessScheduleInstallation,
    setScheduleInstallation,
    setCorrelationId,
    scheduleSuccess = false,
}: IPropsScheduleInstallation) => {
    const { t } = useTranslation(["acquisition", "appointment", "address"]);
    const { callId, setAvailableSlots, availableSlots, selectedSlot, setSelectedSlot, procesarSlot } =
        useScheduleInstallation();

    const { setSnackBarSuccess, setSnackBarError } = useSnackBar();
    const { startFetching, endFetching, endFetchingError } = useFetchState();

    const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
    const [tempSlotInfo, setTempSlotInfo] = useState<ReturnType<typeof procesarSlot> | null>(null);

    const filterContact = contact?.contactAddresses.find((address) => address.type === CONTACT_ADDRES_TYPE_BILLING);
    const infoContact = contact?.person;
    const mainEmail = contact?.emails.find((email) => email.type === "MAIN")?.email ?? ""; 

    useEffect(() => {
        if (selectedSlot) {
            setSuccessScheduleInstallation(true);
            setScheduleInstallation(selectedSlot);
        }
        if (callId) {
            setCorrelationId(callId);
        }
    }, [selectedSlot, callId]);

    const handleCloseConfirmModal = () => {
        setTempSlotInfo(null);
        setIsConfirmModalOpen(false);
    };

    const handleConfirmSlot = async () => {
        if (!selectedSlot || !contact) {
            return;
        }

        setIsConfirmModalOpen(false);
        try {
            startFetching();

            const payload: FieldServiceConfirmAppointment = {
                callId: callId,
                earlyStart: selectedSlot.start,
                lateStart: selectedSlot.finish,
                appointmentStart: selectedSlot.start,
                appointmentFinish: selectedSlot.finish,
                district: filterContact?.addressLine2 ?? "district",
                street: filterContact?.addressLine1 ?? "street",
                city: filterContact?.town ?? "city",
                area: filterContact?.area ?? "area",
                taskType: TASK_TYPE_ADDONS,
                taskTypeCategory: TASK_CATEGORY_ADDONS,
                mcContactEmail: mainEmail,
                customer: `${infoContact?.firstName} ${infoContact?.lastName}`,
                contactName: `${infoContact?.firstName} ${infoContact?.lastName}`,
                mcCustomerIdentityNumber: contact?.contactUuid,
            };

            const response = await processTaskEx(payload);

            if (response?.messageError && response.messageError !== "") {
                setSnackBarError(`DENEGADO: ${response.messageError}`);
                endFetching();

                return;
            }

            const contactBilling = contact.contactAddresses.find((addr) => addr.type === "BILLING");

            const payloadWFE: FieldServiceConfirmAppointmentWFE = {
                addressId: contactBilling?.id?.toString() ?? "UNKNOWN_ADDRESS",
                at: selectedSlot.start,
                contactUuid: contact.contactUuid ?? "UNKNOWN_CONTACT",
                correlationId: Number(new Date().getTime()),
                duration: 30,
                reasonCode: "Equipo dañado",
                source: "TIGO_WEB",
                timeSlotCode: procesarSlot(selectedSlot).time_slot
            };

            await processTasKWFE(callId, payloadWFE);

            setSuccessScheduleInstallation(true);
            setScheduleInstallation(selectedSlot);
            setSnackBarSuccess(t("appointment:confirmSuccess"));
            endFetching();
        } catch (error: any) {
            endFetchingError(error);
            setSnackBarError(`${t("appointment:confirmError")}: ${error.message}`);
        }
    };

     const handleSlotSelection = (slot: ISlot) => {
         const parsedSlot = procesarSlot(slot);
         setTempSlotInfo(parsedSlot);
         setSelectedSlot(slot);
         setIsConfirmModalOpen(true);
     }

    return (
        <>
            <Grid container direction={"row"} spacing={2}>
                <Grid item xs={12}>
                    <Divider textAlign="center">{t("appointment:installationAddress")}</Divider>
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextField
                        disabled
                        fullWidth
                        label={t("appointment:province")}
                        value={filterContact?.addressLine2 ?? "provincia"}
                    />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextField disabled fullWidth label={t("appointment:district")} value={filterContact?.town ?? "distrito"} />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextField
                        disabled
                        fullWidth
                        label={t("appointment:township")}
                        value={filterContact?.addressLine1 ?? "township"}
                    />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextField
                        disabled
                        fullWidth
                        label={t("appointment:neighborhood")}
                        value={filterContact?.area ?? "neighborhood"}
                    />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextField disabled fullWidth label={t("appointment:street")} value={filterContact?.street ?? "street"} />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextField
                        disabled
                        fullWidth
                        label={t("appointment:house")}
                        value={filterContact?.addressLine3 ?? "house"}
                    />
                </Grid>
                <Grid item xs={12}>
                    <Divider textAlign="center">{t("appointment:checkAvailability")}</Divider>
                </Grid>
                <Grid item xs={12}>
                    <ScheduleSlotsForm
                        address={filterContact?.addressLine1 ?? "adress"}
                        callId={callId}
                        district={filterContact?.addressLine2 ?? "district"}
                        latitude={filterContact?.streetNumber ?? ""}
                        longitude={filterContact?.postalCode ?? ""}
                        setAvailableSlots={setAvailableSlots}
                        taskType={TASK_TYPE_ADDONS}
                        taskTypeCategory={TASK_CATEGORY_ADDONS}
                    />
                </Grid>
                {availableSlots && (
                    <div>
                        {scheduleSuccess !== true && (
                            <>
                                <Grid item xs={12}>
                                    <Divider textAlign="center">{t("appointment:availableAppointmentSlots")}</Divider>
                                </Grid>
                                <Grid item xs={12}>
                                    <SlotsForm availableSlots={availableSlots} setSelectedSlot={handleSlotSelection} />
                                </Grid>
                            </>
                        )}
                    </div>
                )}
            </Grid>
            {}
            <ConfirmationModal
                open={isConfirmModalOpen}
                title={t("appointment:confirmTitle")}
                handleClose={handleCloseConfirmModal}
                handleConfirm={handleConfirmSlot}
            >
                <p>{t("appointment:confirmMessage")}</p>
                {tempSlotInfo && (
                    <Grid container marginTop={2} spacing={2}>
                        <Grid item md={6} xs={12}>
                            <TextField
                                disabled
                                fullWidth
                                label={t("appointment:detailAppointment.date")}
                                value={tempSlotInfo.titulo}
                            />
                        </Grid>
                        <Grid item md={6} xs={12}>
                            <TextField disabled fullWidth label={tempSlotInfo.jornada} value={tempSlotInfo.horario} />
                        </Grid>
                    </Grid>
                )}
            </ConfirmationModal>
        </>
    );
};

export default ScheduleInstallation;
