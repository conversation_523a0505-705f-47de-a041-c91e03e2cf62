import {
    ICrossSellActivationFeesResponse,
    ICrossSellTariffPlanResponse,
} from "@modules/crossSell/interfaces/responses/ICrossSellTariffPlansAndConditionalDiscountsResponse";
import * as OfferSelection from "../IOfferSelection";
import {
    IChangeOfferTariffPlanFeeResponse,
    IChangeOfferTariffPlanResponse,
} from "@modules/changeOffer/interfaces/responses/IChangeOfferTariffPlanResponse";
import { IAcquisitionTariffPlanResponse } from "@modules/acquisitionProspect/interfaces/responses/IAcquisitionTariffPlanResponse";
import { IRecurringPrice } from "@features/acquisition/PostpayAcquisition/Postpay/IPostpay";

const mapCrossSellFeesResponse = (response: ICrossSellActivationFeesResponse): OfferSelection.IFees => ({
    upFront: response.upFrontAmount,
    oneOff: response.oneOffAmount,
    upFrontDiscounted: response.discountedUpFrontAmount,
    oneOffDiscounted: response.discountedOneOffAmount,
});

export const filteredRecurringDiscounts = (response: IRecurringPrice[]): OfferSelection.ITariffPlanDiscount[] => {
    if (!response) {
        return [];
    }

    const filteredData = response.filter((item) => item.occurrence);

    if (filteredData.length === 0) {
        return [];
    }

    return filteredData.map((item, index, array) => {
        let newOccurrence = item.occurrence;
        if (newOccurrence && index > 0) {
            newOccurrence -= array[index - 1].occurrence ?? 0;
        }

        return {
            price: item.amountVatIncluded,
            occurrence: newOccurrence ?? 0,
        };
    });
};

export const mapCrossSellTariffPlanResponse = (response: ICrossSellTariffPlanResponse): OfferSelection.ITariffPlan => ({
    code: response.code,
    title: response.description,
    price: response.amountVatIncluded,
    simOnly: response.simOnly,
    category: response.category,
    displayOrder: response.displayOrder,
    commitmentDuration: response.commitmentDuration,
    fees: response.activationFeesVatIncluded ? mapCrossSellFeesResponse(response.activationFeesVatIncluded) : undefined,
    discountedFees: response.discountedActivationFeesVatIncluded
        ? mapCrossSellFeesResponse(response.discountedActivationFeesVatIncluded)
        : undefined,
    discountedPrices: filteredRecurringDiscounts(response.recurringAmountsByOccurrence),
});

export const mapAcquisitionTariffPlanResponse = (
    response: IAcquisitionTariffPlanResponse
): OfferSelection.ITariffPlan => ({
    code: response.code,
    title: response.description,
    price: response.amountVatIncluded,
    simOnly: response.simOnly,
    category: response.category,
    displayOrder: response.displayOrder,
    commitmentDuration: response.commitmentDuration,
    fees: response.activationFeesVatIncluded && response.activationFeesVatExcluded
        ? {
              upFront: response.activationFeesVatIncluded.upFrontAmount,
              oneOff: response.activationFeesVatIncluded.oneOffAmount,
              upFrontDiscounted: response.activationFeesVatIncluded.discountedUpFrontAmount,
              oneOffDiscounted: response.activationFeesVatIncluded.discountedOneOffAmount,

              //Vat excluded
              upFrontVatExcluded: response.activationFeesVatExcluded.upFrontAmount,
              oneOffVatExcluded: response.activationFeesVatExcluded.oneOffAmount,
              upFrontDiscountedVatExcluded: response.activationFeesVatExcluded.discountedUpFrontAmount,
              oneOffDiscountedVatExcluded: response.activationFeesVatExcluded.discountedOneOffAmount,
          }
        : undefined,
    discountedFees: response.discountedActivationFeesVatIncluded && response.discountedActivationFeesVatExcluded
        ? {
              upFront: response.discountedActivationFeesVatIncluded.upFrontAmount,
              oneOff: response.discountedActivationFeesVatIncluded.oneOffAmount,
              upFrontDiscounted: response.discountedActivationFeesVatIncluded.discountedUpFrontAmount,
              oneOffDiscounted: response.discountedActivationFeesVatIncluded.discountedOneOffAmount,

              //Vat excluded
              upFrontVatExcluded: response.discountedActivationFeesVatExcluded.upFrontAmount,
              oneOffVatExcluded: response.discountedActivationFeesVatExcluded.oneOffAmount,
              upFrontDiscountedVatExcluded: response.discountedActivationFeesVatExcluded.discountedUpFrontAmount,
              oneOffDiscountedVatExcluded: response.discountedActivationFeesVatExcluded.discountedOneOffAmount,
              
          }
        : undefined,
    // discountedPrices: filteredRecurringDiscounts(response.recurringAmountsByOccurrence),
    discountedPrices: filteredDiscountsPrices(response.recurringAmountsByOccurrence, response.amountVatIncluded),
    // discountedPrices: response.recurringAmountsByOccurrence.map((item) => ({
    //     price: item.amountVatIncluded,
    // })),

    //Vat excluded
    priceVatExcluded: response.amountVatExcluded,
    discountedPricesVatExcluded: filteredDiscountsPrices(response.recurringAmountsByOccurrence, response.amountVatExcluded),
});

const filteredDiscountsPrices = (
    response: IRecurringPrice[],
    amountVatIncluded: number
): OfferSelection.ITariffPlanDiscount[] => {
    if (!response) {
        return [];
    }
    if (response.some((item) => item.amountVatIncluded === amountVatIncluded)) {
        return [];
    }

    return response.map((item) => ({
        price: item.amountVatIncluded,
    }));
};

export const mapChangeOfferTariffPlanResponse = (
    response: IChangeOfferTariffPlanResponse
): OfferSelection.ITariffPlan => ({
    code: response.code,
    title: response.description,
    price: response.amountVatIncluded,
    simOnly: response.simOnly,
    category: response.category,
    displayOrder: response.displayOrder,
    commitmentDuration: response.commitmentDuration,
    fees: response.fees ? mapChangeOfferTariffPlanFeeResponse(response.fees[0]) : undefined,
    discountedFees: response.discountFees ? mapChangeOfferTariffPlanFeeResponse(response.discountFees[0]) : undefined,
    discountedPrices: filteredRecurringDiscounts(response.recurringAmountsByOccurrence),
});

const mapChangeOfferTariffPlanFeeResponse = (response: IChangeOfferTariffPlanFeeResponse): OfferSelection.IFees => ({
    upFront: response.upFrontAmount,
    oneOff: response.oneOffAmount,
    upFrontDiscounted: response.discountedUpFrontAmount,
    oneOffDiscounted: response.discountedOneOffAmount,
});
