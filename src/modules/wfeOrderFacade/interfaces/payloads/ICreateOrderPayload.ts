import { EWFEOrderFacadeResetPortActions, EWFEOrderFacadeUseCase } from "@modules/wfeOrderFacade/constants";

export type TCreateOrderPayload =
    | ICreateOrderVobbPayload
    | ICreateOrderReplaceAccessoryPayload
    | ICreateOrderResetPortPayload
    | ICreateOrderSpeedProfilePayload
    | ICreateOrderPaymentMethodPayload
    | ICreateOrderUpfrontPaymentPayload
    | ICreateOrderCustomerAcquisitionPayload
    | ICreateOrderAddAddonPayload;

interface ICreateOrderCustomerAcquisitionPayload {
    requestorUsername?: string;
    useCase: EWFEOrderFacadeUseCase.CUSTOMER_ACQUISITION;
    prospectReference: string;
    customOrderId?: string;
}

interface ICreateOrderAddAddonPayload {
    orderId?: string;
    requestorUsername?: string;
    appointmentTimeSlot?: string;
    appointmentDate?: string;
    useCase: EWFEOrderFacadeUseCase.ADD_ADDONS;
    serviceId: string;
    addOn: IAddOn[];
    appointmentConfirmation?: boolean;
}

export interface IAddOn {
    quantity: string;
    catalogCode: string;
}

interface ICreateOrderVobbPayload {
    useCase: EWFEOrderFacadeUseCase.RESEND_VOBB_SETTINGS | EWFEOrderFacadeUseCase.CHANGE_VOBB_PASSWORD;
    serviceId: string;
}

interface ICreateOrderReplaceAccessoryPayload {
    equipmentType: string;
    contactId: string;
    catalogCode: string;
    serviceId: string | undefined;
    useCase: EWFEOrderFacadeUseCase.REPLACE_EQUIPMENT;
}

interface ICreateOrderResetPortPayload {
    useCase: EWFEOrderFacadeUseCase.RESET_PORT;
    serviceId: string;
    resetPortActions: EWFEOrderFacadeResetPortActions[];
}

export interface ICreateOrderSpeedProfilePayload {
    useCase: EWFEOrderFacadeUseCase.CHANGE_SPEED_PROFILE;
    serviceId: string;
    profileCode: string;
}

export enum EPaymentMethodContactChannelType {
    EMAIL = "EMAIL",
    SMS = "SMS",
}

export interface ICreateOrderPaymentMethodPayload {
    useCase: EWFEOrderFacadeUseCase.REGISTER_PAYMENT_METHOD | EWFEOrderFacadeUseCase.MANAGE_PAYMENT_METHOD;
    prospectReference: string;
    contactChannelType: EPaymentMethodContactChannelType;
    contactValue: string;
}

export enum ESaleType {
    PROSPECT = "PROSPECT",
    CROSS_SELL = "CROSS_SELL",
    CHANGE_OFFER = "CHANGE_OFFER",
}

export interface ICreateOrderUpfrontPaymentPayload {
    useCase: EWFEOrderFacadeUseCase.UPFRONT_PAYMENT;
    upfrontAmount: number | undefined;
    prospectReference: string;
    contactChannelType: EPaymentMethodContactChannelType;
    contactValue: string;
    saleType: ESaleType;
}
